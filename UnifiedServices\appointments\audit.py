"""
Enhanced audit logging utilities for the Unified Services system
"""

import json
from datetime import datetime, timedelta
from django.contrib.auth import get_user_model
from django.contrib.auth.signals import user_logged_in, user_logged_out, user_login_failed
from django.db.models.signals import post_save, post_delete, pre_save
from django.db import models
from django.dispatch import receiver
from django.utils import timezone
from django.core.cache import cache
from .models import AuditLog, SecurityEvent, UserProfile, Appointment, AppointmentRequirement

User = get_user_model()


class AuditLogger:
    """Enhanced audit logging utility class"""
    
    @staticmethod
    def log_user_action(user, action, description, request=None, appointment=None, additional_data=None):
        """Log user actions with comprehensive details"""
        return AuditLog.log_action(
            user=user,
            action=action,
            description=description,
            appointment=appointment,
            request=request,
            additional_data=additional_data
        )
    
    @staticmethod
    def log_system_event(action, description, additional_data=None):
        """Log system-level events"""
        return AuditLog.objects.create(
            action=action,
            description=description,
            additional_data=json.dumps(additional_data) if additional_data else ''
        )
    
    @staticmethod
    def log_security_violation(user, violation_type, description, request=None, severity='medium'):
        """Log security violations"""
        # Create audit log entry
        AuditLog.log_action(
            user=user,
            action='security_violation',
            description=description,
            request=request,
            additional_data={'violation_type': violation_type, 'severity': severity}
        )
        
        # Create security event
        if request:
            SecurityEvent.objects.create(
                event_type=violation_type,
                severity=severity,
                user=user,
                ip_address=AuditLog.get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                description=description,
                request_path=request.path
            )
    
    @staticmethod
    def get_user_activity_summary(user, days=30):
        """Get user activity summary for the past N days"""
        start_date = timezone.now() - timedelta(days=days)
        
        logs = AuditLog.objects.filter(
            user=user,
            timestamp__gte=start_date
        ).values('action').annotate(
            count=models.Count('action')
        ).order_by('-count')
        
        return {
            'total_actions': sum(log['count'] for log in logs),
            'action_breakdown': list(logs),
            'period_days': days,
            'start_date': start_date,
            'end_date': timezone.now()
        }
    
    @staticmethod
    def get_system_activity_summary(days=7):
        """Get system-wide activity summary"""
        start_date = timezone.now() - timedelta(days=days)
        
        # Get action counts
        action_counts = AuditLog.objects.filter(
            timestamp__gte=start_date
        ).values('action').annotate(
            count=models.Count('action')
        ).order_by('-count')
        
        # Get daily activity
        daily_activity = AuditLog.objects.filter(
            timestamp__gte=start_date
        ).extra(
            select={'day': 'date(timestamp)'}
        ).values('day').annotate(
            count=models.Count('id')
        ).order_by('day')
        
        # Get security events
        security_events = SecurityEvent.objects.filter(
            timestamp__gte=start_date,
            resolved=False
        ).count()
        
        return {
            'action_counts': list(action_counts),
            'daily_activity': list(daily_activity),
            'unresolved_security_events': security_events,
            'period_days': days
        }


# Signal handlers for automatic audit logging

@receiver(user_logged_in)
def log_user_login(sender, request, user, **kwargs):
    """Log successful user logins"""
    AuditLog.log_action(
        user=user,
        action='login',
        description=f'User {user.username} logged in successfully',
        request=request
    )


@receiver(user_logged_out)
def log_user_logout(sender, request, user, **kwargs):
    """Log user logouts"""
    if user:  # user might be None if session expired
        AuditLog.log_action(
            user=user,
            action='logout',
            description=f'User {user.username} logged out',
            request=request
        )


@receiver(user_login_failed)
def log_failed_login(sender, credentials, request, **kwargs):
    """Log failed login attempts and detect brute force attacks"""
    username = credentials.get('username', 'unknown')
    ip_address = AuditLog.get_client_ip(request)
    
    # Log the failed attempt
    AuditLog.log_action(
        action='login_failed',
        description=f'Failed login attempt for username: {username}',
        request=request,
        additional_data={'username': username}
    )
    
    # Track failed attempts for brute force detection
    cache_key = f"failed_login_{ip_address}"
    failed_attempts = cache.get(cache_key, 0) + 1
    cache.set(cache_key, failed_attempts, 300)  # 5 minutes
    
    # Log security event for multiple failed attempts
    if failed_attempts >= 3:
        SecurityEvent.objects.create(
            event_type='multiple_failed_logins',
            severity='medium' if failed_attempts < 5 else 'high',
            ip_address=ip_address,
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            description=f'Multiple failed login attempts ({failed_attempts}) for username: {username}',
            request_path=request.path
        )


@receiver(post_save, sender=User)
def log_user_changes(sender, instance, created, **kwargs):
    """Log user creation and updates"""
    if created:
        AuditLog.objects.create(
            user=instance,
            action='user_created',
            description=f'New user account created: {instance.username}'
        )
    else:
        # Check if password was changed
        if hasattr(instance, '_password_changed'):
            AuditLog.objects.create(
                user=instance,
                action='password_changed',
                description=f'Password changed for user: {instance.username}'
            )
        else:
            AuditLog.objects.create(
                user=instance,
                action='user_updated',
                description=f'User account updated: {instance.username}'
            )


@receiver(post_save, sender=UserProfile)
def log_profile_changes(sender, instance, created, **kwargs):
    """Log user profile changes, especially role changes"""
    if not created:
        # Check if role was changed
        try:
            old_profile = UserProfile.objects.get(pk=instance.pk)
            if hasattr(old_profile, '_original_role') and old_profile._original_role != instance.role:
                AuditLog.objects.create(
                    user=instance.user,
                    action='role_changed',
                    description=f'User role changed from {old_profile._original_role} to {instance.role}',
                    additional_data=json.dumps({
                        'old_role': old_profile._original_role,
                        'new_role': instance.role
                    })
                )
        except UserProfile.DoesNotExist:
            pass


@receiver(pre_save, sender=UserProfile)
def track_role_changes(sender, instance, **kwargs):
    """Track role changes before saving"""
    if instance.pk:
        try:
            old_instance = UserProfile.objects.get(pk=instance.pk)
            instance._original_role = old_instance.role
        except UserProfile.DoesNotExist:
            pass


@receiver(post_save, sender=Appointment)
def log_appointment_changes(sender, instance, created, **kwargs):
    """Log appointment creation and updates"""
    if created:
        AuditLog.objects.create(
            user=instance.student,
            action='appointment_created',
            description=f'New appointment created: {instance.appointment_id} for {instance.service.name}',
            appointment=instance
        )
    else:
        # Check for status changes
        if hasattr(instance, '_original_status') and instance._original_status != instance.status:
            AuditLog.objects.create(
                user=instance.student,
                action='appointment_updated',
                description=f'Appointment {instance.appointment_id} status changed from {instance._original_status} to {instance.status}',
                appointment=instance,
                additional_data=json.dumps({
                    'old_status': instance._original_status,
                    'new_status': instance.status
                })
            )


@receiver(pre_save, sender=Appointment)
def track_appointment_changes(sender, instance, **kwargs):
    """Track appointment changes before saving"""
    if instance.pk:
        try:
            old_instance = Appointment.objects.get(pk=instance.pk)
            instance._original_status = old_instance.status
        except Appointment.DoesNotExist:
            pass


@receiver(post_delete, sender=Appointment)
def log_appointment_deletion(sender, instance, **kwargs):
    """Log appointment deletions"""
    AuditLog.objects.create(
        action='appointment_deleted',
        description=f'Appointment deleted: {instance.appointment_id} for {instance.service.name}',
        additional_data=json.dumps({
            'appointment_id': instance.appointment_id,
            'student_username': instance.student.username,
            'service_name': instance.service.name,
            'status': instance.status
        })
    )


@receiver(post_save, sender=AppointmentRequirement)
def log_requirement_changes(sender, instance, created, **kwargs):
    """Log requirement completion changes"""
    if not created and hasattr(instance, '_original_completed'):
        if instance._original_completed != instance.is_completed:
            action = 'requirement_completed' if instance.is_completed else 'requirement_uncompleted'
            AuditLog.objects.create(
                user=instance.completed_by if instance.is_completed else None,
                action=action,
                description=f'Requirement {"completed" if instance.is_completed else "uncompleted"}: {instance.requirement.name} for appointment {instance.appointment.appointment_id}',
                appointment=instance.appointment
            )


@receiver(pre_save, sender=AppointmentRequirement)
def track_requirement_changes(sender, instance, **kwargs):
    """Track requirement changes before saving"""
    if instance.pk:
        try:
            old_instance = AppointmentRequirement.objects.get(pk=instance.pk)
            instance._original_completed = old_instance.is_completed
        except AppointmentRequirement.DoesNotExist:
            pass


class AuditReportGenerator:
    """Generate audit reports for administrators"""
    
    @staticmethod
    def generate_user_activity_report(start_date, end_date, user=None):
        """Generate user activity report"""
        queryset = AuditLog.objects.filter(
            timestamp__range=[start_date, end_date]
        )
        
        if user:
            queryset = queryset.filter(user=user)
        
        return {
            'total_activities': queryset.count(),
            'activities_by_action': list(
                queryset.values('action').annotate(
                    count=models.Count('action')
                ).order_by('-count')
            ),
            'activities_by_user': list(
                queryset.values('user__username').annotate(
                    count=models.Count('user')
                ).order_by('-count')
            ),
            'daily_breakdown': list(
                queryset.extra(
                    select={'day': 'date(timestamp)'}
                ).values('day').annotate(
                    count=models.Count('id')
                ).order_by('day')
            ),
            'period': {
                'start': start_date,
                'end': end_date
            }
        }
    
    @staticmethod
    def generate_security_report(start_date, end_date):
        """Generate security events report"""
        security_events = SecurityEvent.objects.filter(
            timestamp__range=[start_date, end_date]
        )
        
        return {
            'total_events': security_events.count(),
            'events_by_type': list(
                security_events.values('event_type').annotate(
                    count=models.Count('event_type')
                ).order_by('-count')
            ),
            'events_by_severity': list(
                security_events.values('severity').annotate(
                    count=models.Count('severity')
                ).order_by('-count')
            ),
            'unresolved_events': security_events.filter(resolved=False).count(),
            'top_ips': list(
                security_events.values('ip_address').annotate(
                    count=models.Count('ip_address')
                ).order_by('-count')[:10]
            ),
            'period': {
                'start': start_date,
                'end': end_date
            }
        }
