from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from appointments.models import UserProfile, Department, Service, ServiceRequirement


class Command(BaseCommand):
    help = 'Set up sample data for the Unified Student Services system'

    def handle(self, *args, **options):
        self.stdout.write('Setting up sample data...')

        # Create departments
        departments_data = [
            {
                'name': 'Registrar Office',
                'code': 'REG',
                'description': 'Student records, transcripts, and academic documents'
            },
            {
                'name': 'Accounting Office',
                'code': 'ACC',
                'description': 'Student financial accounts and payment processing'
            },
            {
                'name': 'Guidance Office',
                'code': 'GUI',
                'description': 'Student counseling and guidance services'
            },
            {
                'name': 'Student Affairs',
                'code': 'SA',
                'description': 'Student activities and welfare services'
            }
        ]

        for dept_data in departments_data:
            dept, created = Department.objects.get_or_create(
                code=dept_data['code'],
                defaults=dept_data
            )
            if created:
                self.stdout.write(f'Created department: {dept.name}')

        # Create services and requirements
        services_data = [
            {
                'department_code': 'REG',
                'name': 'Certificate of Good Moral Character',
                'description': 'Official certificate of good moral standing',
                'processing_time': '3-5 business days',
                'requirements': [
                    {'name': 'Completed Application Form', 'description': 'Fill out the official application form', 'requires_upload': False},
                    {'name': 'Valid ID Copy', 'description': 'Photocopy of valid government ID', 'requires_upload': True},
                    {'name': 'Payment Receipt', 'description': 'Proof of payment for processing fee', 'requires_upload': True},
                ]
            },
            {
                'department_code': 'REG',
                'name': 'Official Transcript of Records',
                'description': 'Complete academic transcript',
                'processing_time': '5-7 business days',
                'requirements': [
                    {'name': 'Request Form', 'description': 'Official transcript request form', 'requires_upload': False},
                    {'name': 'Payment Receipt', 'description': 'Proof of payment for transcript fee', 'requires_upload': True},
                    {'name': 'Valid ID', 'description': 'Present valid government ID', 'requires_upload': False},
                ]
            },
            {
                'department_code': 'ACC',
                'name': 'Certificate of No Outstanding Balance',
                'description': 'Certification of cleared financial obligations',
                'processing_time': '2-3 business days',
                'requirements': [
                    {'name': 'Application Form', 'description': 'Complete the clearance application', 'requires_upload': False},
                    {'name': 'Student ID', 'description': 'Present student identification card', 'requires_upload': False},
                ]
            },
            {
                'department_code': 'GUI',
                'name': 'Counseling Session Appointment',
                'description': 'Schedule individual counseling session',
                'processing_time': '1-2 business days',
                'requirements': [
                    {'name': 'Appointment Request', 'description': 'Submit counseling appointment request', 'requires_upload': False},
                    {'name': 'Concern Description', 'description': 'Brief description of counseling needs', 'requires_upload': False},
                ]
            }
        ]

        for service_data in services_data:
            department = Department.objects.get(code=service_data['department_code'])
            service, created = Service.objects.get_or_create(
                department=department,
                name=service_data['name'],
                defaults={
                    'description': service_data['description'],
                    'processing_time': service_data['processing_time']
                }
            )
            if created:
                self.stdout.write(f'Created service: {service.name}')

                # Create requirements for this service
                for i, req_data in enumerate(service_data['requirements']):
                    ServiceRequirement.objects.create(
                        service=service,
                        name=req_data['name'],
                        description=req_data['description'],
                        requires_upload=req_data['requires_upload'],
                        order=i + 1
                    )

        # Create sample users
        users_data = [
            {
                'username': 'student1',
                'email': '<EMAIL>',
                'first_name': 'Juan',
                'last_name': 'Dela Cruz',
                'role': 'student',
                'student_id': 'STU2024001'
            },
            {
                'username': 'registrar1',
                'email': '<EMAIL>',
                'first_name': 'Maria',
                'last_name': 'Santos',
                'role': 'office_staff',
                'department_code': 'REG'
            },
            {
                'username': 'accounting1',
                'email': '<EMAIL>',
                'first_name': 'Jose',
                'last_name': 'Garcia',
                'role': 'office_staff',
                'department_code': 'ACC'
            },
            {
                'username': 'admin1',
                'email': '<EMAIL>',
                'first_name': 'Admin',
                'last_name': 'User',
                'role': 'admin'
            }
        ]

        for user_data in users_data:
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults={
                    'email': user_data['email'],
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name']
                }
            )
            if created:
                user.set_password('password123')  # Default password
                user.save()
                self.stdout.write(f'Created user: {user.username}')

                # Update user profile
                profile = user.userprofile
                profile.role = user_data['role']
                if 'student_id' in user_data:
                    profile.student_id = user_data['student_id']
                if 'department_code' in user_data:
                    profile.department = Department.objects.get(code=user_data['department_code'])
                profile.save()

        self.stdout.write(
            self.style.SUCCESS('Successfully set up sample data!')
        )
        self.stdout.write('Default password for all users: password123')
