{% extends 'base.html' %}

{% block title %}QR Code Scanner - JHCSC Unified Student Services{% endblock %}

{% block page_title %}QR Code Scanner{% endblock %}

{% block extra_head %}
<!-- Custom Tailwind Config for Emerald & Gold Theme -->
<script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    emerald: {
                        50: '#ecfdf5',
                        100: '#d1fae5',
                        200: '#a7f3d0',
                        300: '#6ee7b7',
                        400: '#34d399',
                        500: '#10b981',
                        600: '#059669',
                        700: '#047857',
                        800: '#065f46',
                        900: '#064e3b',
                        950: '#022c22'
                    },
                    gold: {
                        50: '#fffbeb',
                        100: '#fef3c7',
                        200: '#fde68a',
                        300: '#fcd34d',
                        400: '#fbbf24',
                        500: '#f59e0b',
                        600: '#d97706',
                        700: '#b45309',
                        800: '#92400e',
                        900: '#78350f'
                    }
                },
                animation: {
                    'fade-in': 'fadeIn 0.6s ease-out',
                    'slide-up': 'slideUp 0.6s ease-out',
                    'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    'scanner-sweep': 'scannerSweep 2s linear infinite',
                },
                keyframes: {
                    fadeIn: {
                        '0%': { opacity: '0' },
                        '100%': { opacity: '1' }
                    },
                    slideUp: {
                        '0%': { opacity: '0', transform: 'translateY(20px)' },
                        '100%': { opacity: '1', transform: 'translateY(0)' }
                    },
                    scannerSweep: {
                        '0%': { transform: 'translateY(-100%)' },
                        '100%': { transform: 'translateY(100%)' }
                    }
                }
            }
        }
    }
</script>

<style>
    .card-hover {
        transition: all 0.3s ease;
    }
    .card-hover:hover {
        transform: translateY(-2px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    .hero-gradient {
        background: linear-gradient(135deg, #064e3b 0%, #047857 50%, #059669 100%);
    }
    .scanner-line {
        background: linear-gradient(90deg, transparent, #10b981, transparent);
        height: 2px;
        animation: scannerSweep 2s linear infinite;
    }
</style>
{% endblock %}

{% block mobile_nav %}
{% include 'navigation/staff_nav.html' %}
{% endblock %}

{% block desktop_nav %}
{% include 'navigation/staff_nav.html' %}
{% endblock %}

{% block content %}
<div class="animate-fade-in">
    <!-- Header Section -->
    <div class="hero-gradient rounded-2xl p-8 mb-8 text-white shadow-2xl relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <svg class="w-full h-full" viewBox="0 0 100 100" fill="none">
                <defs>
                    <pattern id="qr-grid" width="10" height="10" patternUnits="userSpaceOnUse">
                        <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" stroke-width="0.5"/>
                    </pattern>
                </defs>
                <rect width="100" height="100" fill="url(#qr-grid)" />
            </svg>
        </div>

        <div class="relative flex items-center justify-between">
            <div class="flex-1">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.75 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 013.75 9.375v-4.5zM3.75 14.625c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5a1.125 1.125 0 01-1.125-1.125v-4.5zM13.5 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 0113.5 9.375v-4.5z" />
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 6.75h.75v.75h-.75v-.75zM6.75 16.5h.75v.75h-.75v-.75zM16.5 6.75h.75v.75h-.75v-.75zM13.5 13.5h4.5v4.5h-4.5v-4.5z" />
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold mb-1">QR Code Scanner</h1>
                        <p class="text-emerald-100 text-lg font-medium">Document Validation Portal</p>
                    </div>
                </div>
                <p class="text-emerald-200 text-sm">Scan student QR codes to validate and release documents efficiently</p>
            </div>
            <div class="hidden lg:block">
                <div class="w-24 h-24 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center backdrop-blur-sm animate-pulse-slow">
                    <svg class="w-12 h-12 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3.75 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 013.75 9.375v-4.5zM3.75 14.625c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5a1.125 1.125 0 01-1.125-1.125v-4.5zM13.5 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 0113.5 9.375v-4.5z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 6.75h.75v.75h-.75v-.75zM6.75 16.5h.75v.75h-.75v-.75zM16.5 6.75h.75v.75h-.75v-.75zM13.5 13.5h4.5v4.5h-4.5v-4.5z" />
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Scanner Section -->
        <div class="bg-white shadow-xl rounded-2xl border border-gray-100 card-hover">
            <div class="px-6 py-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-semibold text-gray-900">Camera Scanner</h3>
                    <div class="w-8 h-8 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                    </div>
                </div>

                <!-- Camera View -->
                <div class="relative mb-6">
                    <video id="qr-video"
                           class="w-full h-80 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl object-cover shadow-inner border-4 border-emerald-200"
                           style="display: none;">
                    </video>
                    <div id="camera-placeholder"
                         class="w-full h-80 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center shadow-inner border-4 border-gray-200">
                        <div class="text-center p-8">
                            <div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-emerald-100 to-emerald-200 rounded-2xl flex items-center justify-center">
                                <svg class="w-10 h-10 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-2">Camera Ready</h4>
                            <p class="text-gray-500 mb-4">Click "Start Camera" to begin scanning QR codes</p>
                            <div class="flex items-center justify-center text-sm text-gray-400">
                                <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                Ensure good lighting for best results
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Scanning overlay -->
                    <div id="scan-overlay"
                         class="absolute inset-0 pointer-events-none rounded-2xl"
                         style="display: none;">
                        <div class="absolute inset-6 border-4 border-emerald-400 rounded-2xl">
                            <!-- Corner indicators -->
                            <div class="absolute -top-2 -left-2 w-8 h-8 border-t-4 border-l-4 border-emerald-500 rounded-tl-xl"></div>
                            <div class="absolute -top-2 -right-2 w-8 h-8 border-t-4 border-r-4 border-emerald-500 rounded-tr-xl"></div>
                            <div class="absolute -bottom-2 -left-2 w-8 h-8 border-b-4 border-l-4 border-emerald-500 rounded-bl-xl"></div>
                            <div class="absolute -bottom-2 -right-2 w-8 h-8 border-b-4 border-r-4 border-emerald-500 rounded-br-xl"></div>
                            <!-- Scanning line -->
                            <div class="absolute top-0 left-0 right-0 scanner-line"></div>
                        </div>
                        <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-emerald-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                            Scanning...
                        </div>
                    </div>
                </div>

                <!-- Camera Controls -->
                <div class="flex space-x-4">
                    <button id="start-camera-btn"
                            onclick="startCamera()"
                            class="flex-1 bg-gradient-to-r from-emerald-600 to-emerald-700 py-3 px-6 border border-transparent rounded-xl shadow-lg text-sm font-semibold text-white hover:from-emerald-700 hover:to-emerald-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-all duration-200">
                        <svg class="inline-block -ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                        Start Camera
                    </button>
                    <button id="stop-camera-btn"
                            onclick="stopCamera()"
                            style="display: none;"
                            class="flex-1 bg-gradient-to-r from-red-600 to-red-700 py-3 px-6 border border-transparent rounded-xl shadow-lg text-sm font-semibold text-white hover:from-red-700 hover:to-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200">
                        <svg class="inline-block -ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
                        </svg>
                        Stop Camera
                    </button>
                </div>

                <!-- Manual Input -->
                <div class="mt-8 pt-6 border-t border-gray-200">
                    <div class="flex items-center mb-4">
                        <div class="w-6 h-6 bg-gradient-to-r from-gold-500 to-gold-600 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900">Manual QR Code Input</h4>
                    </div>
                    <p class="text-sm text-gray-500 mb-4">Alternative method: paste or type QR code data directly</p>
                    <div class="flex space-x-3">
                        <input type="text"
                               id="manual-qr-input"
                               placeholder="Paste QR code data here..."
                               class="flex-1 block w-full rounded-xl border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500 sm:text-sm px-4 py-3">
                        <button onclick="validateManualInput()"
                                class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-semibold rounded-xl shadow-lg text-white bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-all duration-200">
                            <svg class="inline-block -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Validate
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Scan Results</h3>
                
                <!-- Status Display -->
                <div id="scan-status" class="mb-4">
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
                        </svg>
                        <p class="text-gray-500">Waiting for QR code scan...</p>
                    </div>
                </div>

                <!-- Appointment Details (hidden by default) -->
                <div id="appointment-details" style="display: none;">
                    <div class="border border-green-200 rounded-lg p-4 bg-green-50">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="ml-3 flex-1">
                                <h4 class="text-sm font-medium text-green-800">Valid QR Code</h4>
                                <div class="mt-2 text-sm text-green-700">
                                    <dl class="grid grid-cols-1 gap-2">
                                        <div>
                                            <dt class="font-medium">Appointment ID:</dt>
                                            <dd id="detail-appointment-id">-</dd>
                                        </div>
                                        <div>
                                            <dt class="font-medium">Student:</dt>
                                            <dd id="detail-student-name">-</dd>
                                        </div>
                                        <div>
                                            <dt class="font-medium">Service:</dt>
                                            <dd id="detail-service">-</dd>
                                        </div>
                                        <div>
                                            <dt class="font-medium">Department:</dt>
                                            <dd id="detail-department">-</dd>
                                        </div>
                                        <div>
                                            <dt class="font-medium">Status:</dt>
                                            <dd id="detail-status">-</dd>
                                        </div>
                                    </dl>
                                </div>
                                <div class="mt-4">
                                    <button id="claim-btn" 
                                            onclick="claimAppointment()"
                                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        Release Documents
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Error Display (hidden by default) -->
                <div id="error-display" style="display: none;">
                    <div class="border border-red-200 rounded-lg p-4 bg-red-50">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h4 class="text-sm font-medium text-red-800">Invalid QR Code</h4>
                                <p id="error-message" class="mt-1 text-sm text-red-700">-</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include QR Code Scanner Library -->
<script src="https://unpkg.com/qr-scanner@1.4.2/qr-scanner.umd.min.js"></script>

<script>
let qrScanner = null;
let currentAppointmentId = null;

async function startCamera() {
    try {
        const video = document.getElementById('qr-video');
        const placeholder = document.getElementById('camera-placeholder');
        const overlay = document.getElementById('scan-overlay');
        const startBtn = document.getElementById('start-camera-btn');
        const stopBtn = document.getElementById('stop-camera-btn');

        // Show video, hide placeholder
        video.style.display = 'block';
        placeholder.style.display = 'none';
        overlay.style.display = 'block';
        startBtn.style.display = 'none';
        stopBtn.style.display = 'block';

        // Initialize QR Scanner
        qrScanner = new QrScanner(
            video,
            result => {
                console.log('QR Code detected:', result.data);
                validateQRCode(result.data);
            },
            {
                returnDetailedScanResult: true,
                highlightScanRegion: true,
                highlightCodeOutline: true,
            }
        );

        await qrScanner.start();
        
    } catch (error) {
        console.error('Error starting camera:', error);
        alert('Error accessing camera. Please ensure camera permissions are granted.');
        stopCamera();
    }
}

function stopCamera() {
    if (qrScanner) {
        qrScanner.stop();
        qrScanner.destroy();
        qrScanner = null;
    }

    const video = document.getElementById('qr-video');
    const placeholder = document.getElementById('camera-placeholder');
    const overlay = document.getElementById('scan-overlay');
    const startBtn = document.getElementById('start-camera-btn');
    const stopBtn = document.getElementById('stop-camera-btn');

    video.style.display = 'none';
    placeholder.style.display = 'flex';
    overlay.style.display = 'none';
    startBtn.style.display = 'block';
    stopBtn.style.display = 'none';
}

function validateManualInput() {
    const input = document.getElementById('manual-qr-input');
    const qrData = input.value.trim();
    
    if (!qrData) {
        alert('Please enter QR code data');
        return;
    }
    
    validateQRCode(qrData);
}

async function validateQRCode(qrData) {
    try {
        const response = await fetch('{% url "validate_qr_code" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            },
            body: JSON.stringify({ qr_data: qrData })
        });

        const result = await response.json();
        
        if (result.success) {
            showAppointmentDetails(result.appointment);
        } else {
            showError(result.message);
        }
        
    } catch (error) {
        console.error('Validation error:', error);
        showError('Network error occurred while validating QR code');
    }
}

function showAppointmentDetails(appointment) {
    currentAppointmentId = appointment.id;
    
    // Hide status and error displays
    document.getElementById('scan-status').style.display = 'none';
    document.getElementById('error-display').style.display = 'none';
    
    // Populate appointment details
    document.getElementById('detail-appointment-id').textContent = appointment.id;
    document.getElementById('detail-student-name').textContent = appointment.student_name;
    document.getElementById('detail-service').textContent = appointment.service;
    document.getElementById('detail-department').textContent = appointment.department;
    document.getElementById('detail-status').textContent = appointment.status;
    
    // Show appointment details
    document.getElementById('appointment-details').style.display = 'block';
}

function showError(message) {
    currentAppointmentId = null;
    
    // Hide status and appointment displays
    document.getElementById('scan-status').style.display = 'none';
    document.getElementById('appointment-details').style.display = 'none';
    
    // Show error
    document.getElementById('error-message').textContent = message;
    document.getElementById('error-display').style.display = 'block';
}

async function claimAppointment() {
    if (!currentAppointmentId) {
        alert('No appointment selected');
        return;
    }
    
    const btn = document.getElementById('claim-btn');
    const originalText = btn.innerHTML;
    
    // Show loading state
    btn.disabled = true;
    btn.innerHTML = `
        <svg class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Processing...
    `;
    
    try {
        const response = await fetch(`{% url "claim_appointment" "PLACEHOLDER" %}`.replace('PLACEHOLDER', currentAppointmentId), {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            }
        });

        const result = await response.json();
        
        if (result.success) {
            alert('Documents released successfully!');
            // Reset the interface
            resetInterface();
        } else {
            alert('Error: ' + result.message);
            btn.disabled = false;
            btn.innerHTML = originalText;
        }
        
    } catch (error) {
        console.error('Claim error:', error);
        alert('Network error occurred while claiming appointment');
        btn.disabled = false;
        btn.innerHTML = originalText;
    }
}

function resetInterface() {
    currentAppointmentId = null;
    
    // Hide all result displays
    document.getElementById('appointment-details').style.display = 'none';
    document.getElementById('error-display').style.display = 'none';
    
    // Show waiting status
    document.getElementById('scan-status').style.display = 'block';
    
    // Clear manual input
    document.getElementById('manual-qr-input').value = '';
    
    // Reset claim button
    const btn = document.getElementById('claim-btn');
    btn.disabled = false;
    btn.innerHTML = `
        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        Release Documents
    `;
}

// Add CSRF token to page
document.addEventListener('DOMContentLoaded', function() {
    if (!document.querySelector('[name=csrfmiddlewaretoken]')) {
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrfmiddlewaretoken';
        csrfToken.value = '{{ csrf_token }}';
        document.body.appendChild(csrfToken);
    }
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (qrScanner) {
        qrScanner.stop();
        qrScanner.destroy();
    }
});
</script>
{% endblock %}
