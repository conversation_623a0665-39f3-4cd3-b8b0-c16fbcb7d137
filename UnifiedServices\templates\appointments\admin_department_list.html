{% extends 'base.html' %}
{% load static %}

{% block title %}Department Management - Admin - JHCSC Unified Services{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Department Management</h1>
                    <p class="mt-1 text-sm text-gray-500">Manage departments and services</p>
                </div>
                <a href="{% url 'admin_dashboard' %}" class="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700">
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <nav class="flex space-x-8 mb-8">
            <a href="{% url 'admin_dashboard' %}" class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">
                Dashboard
            </a>
            <a href="{% url 'admin_appointment_list' %}" class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">
                All Appointments
            </a>
            <a href="{% url 'admin_user_list' %}" class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">
                User Management
            </a>
            <a href="{% url 'admin_department_list' %}" class="bg-indigo-100 text-indigo-700 px-3 py-2 rounded-md text-sm font-medium">
                Departments
            </a>
            <a href="{% url 'admin_reports' %}" class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">
                Reports
            </a>
        </nav>

        <!-- Departments Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {% for dept_stat in departments_with_stats %}
            <div class="bg-white shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:p-6">
                    <!-- Department Header -->
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">{{ dept_stat.department.name }}</h3>
                            <p class="text-sm text-gray-500">{{ dept_stat.department.description }}</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                {{ dept_stat.services_count }} services
                            </span>
                        </div>
                    </div>

                    <!-- Department Statistics -->
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="bg-gray-50 rounded-lg p-3">
                            <div class="text-sm font-medium text-gray-500">Total Appointments</div>
                            <div class="text-2xl font-bold text-gray-900">{{ dept_stat.appointments_count }}</div>
                        </div>
                        <div class="bg-gray-50 rounded-lg p-3">
                            <div class="text-sm font-medium text-gray-500">Pending</div>
                            <div class="text-2xl font-bold text-yellow-600">{{ dept_stat.pending_count }}</div>
                        </div>
                    </div>

                    <!-- Staff Information -->
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Staff Members</h4>
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                            <span class="text-sm text-gray-600">{{ dept_stat.staff_count }} staff members assigned</span>
                        </div>
                    </div>

                    <!-- Services List -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Available Services</h4>
                        <div class="space-y-2">
                            {% for service in dept_stat.department.services.all %}
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ service.name }}</div>
                                    <div class="text-xs text-gray-500">{{ service.description|truncatechars:50 }}</div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    {% if service.is_active %}
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                            Active
                                        </span>
                                    {% else %}
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                            Inactive
                                        </span>
                                    {% endif %}
                                    <span class="text-xs text-gray-500">
                                        {{ service.requirements.count }} req.
                                    </span>
                                </div>
                            </div>
                            {% empty %}
                            <div class="text-sm text-gray-500 italic">No services configured</div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Contact Information -->
                    {% if dept_stat.department.contact_email or dept_stat.department.contact_phone %}
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Contact Information</h4>
                        <div class="space-y-1">
                            {% if dept_stat.department.contact_email %}
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                {{ dept_stat.department.contact_email }}
                            </div>
                            {% endif %}
                            {% if dept_stat.department.contact_phone %}
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                                {{ dept_stat.department.contact_phone }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Location Information -->
                    {% if dept_stat.department.location %}
                    <div class="mt-4">
                        <div class="flex items-center text-sm text-gray-600">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            {{ dept_stat.department.location }}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% empty %}
            <div class="col-span-2">
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No departments</h3>
                    <p class="mt-1 text-sm text-gray-500">No departments have been configured yet.</p>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- System Configuration Note -->
        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">Department Configuration</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <p>Department and service configuration is currently managed through the Django admin interface. 
                        To add or modify departments, services, and requirements, please use the Django admin panel.</p>
                    </div>
                    <div class="mt-4">
                        <div class="flex space-x-4">
                            <a href="/admin/" target="_blank" class="bg-blue-100 text-blue-800 px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-200">
                                Open Django Admin
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
