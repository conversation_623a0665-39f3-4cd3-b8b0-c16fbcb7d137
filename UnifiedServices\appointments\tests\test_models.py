"""
Test cases for appointment models
"""

from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import timedelta
import json

from appointments.models import (
    UserProfile, Department, Service, ServiceRequirement,
    Appointment, AppointmentRequirement, AuditLog, SecurityEvent, FileAccessLog
)


class UserProfileModelTest(TestCase):
    """Test UserProfile model functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_user_profile_creation(self):
        """Test UserProfile is created automatically"""
        self.assertTrue(hasattr(self.user, 'userprofile'))
        self.assertEqual(self.user.userprofile.role, 'student')
    
    def test_user_profile_str_method(self):
        """Test UserProfile string representation"""
        expected = f"{self.user.get_full_name()} (Student)"
        self.assertEqual(str(self.user.userprofile), expected)
    
    def test_role_choices(self):
        """Test role validation"""
        profile = self.user.userprofile
        
        # Test valid roles
        for role, _ in UserProfile.ROLE_CHOICES:
            profile.role = role
            profile.full_clean()  # Should not raise ValidationError
        
        # Test invalid role
        profile.role = 'invalid_role'
        with self.assertRaises(ValidationError):
            profile.full_clean()


class DepartmentModelTest(TestCase):
    """Test Department model functionality"""
    
    def setUp(self):
        self.department = Department.objects.create(
            name='Registrar',
            code='REG',
            description='Student records and enrollment'
        )
    
    def test_department_creation(self):
        """Test department creation"""
        self.assertEqual(self.department.name, 'Registrar')
        self.assertTrue(self.department.is_active)
    
    def test_department_str_method(self):
        """Test department string representation"""
        self.assertEqual(str(self.department), 'Registrar')
    
    def test_department_code_uniqueness(self):
        """Test department code uniqueness"""
        # First department should be created successfully
        dept1 = Department.objects.create(
            name='Student Affairs',
            code='SA',
            description='Student support services'
        )
        self.assertEqual(dept1.code, 'SA')

        # Second department with same code should raise error
        with self.assertRaises(Exception):
            Department.objects.create(
                name='Student Activities',
                code='SA',  # Duplicate code
                description='Student activities'
            )


class ServiceModelTest(TestCase):
    """Test Service model functionality"""
    
    def setUp(self):
        self.department = Department.objects.create(
            name='Registrar',
            code='REG',
            description='Student records'
        )
        self.service = Service.objects.create(
            name='Transcript Request',
            description='Official transcript processing',
            department=self.department,
            processing_time='5 business days'
        )
    
    def test_service_creation(self):
        """Test service creation"""
        self.assertEqual(self.service.name, 'Transcript Request')
        self.assertEqual(self.service.department, self.department)
        self.assertTrue(self.service.is_active)
    
    def test_service_str_method(self):
        """Test service string representation"""
        expected = f"{self.department.name} - {self.service.name}"
        self.assertEqual(str(self.service), expected)


class AppointmentModelTest(TestCase):
    """Test Appointment model functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='student1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.department = Department.objects.create(
            name='Registrar',
            code='REG',
            description='Student records'
        )
        self.service = Service.objects.create(
            name='Transcript Request',
            description='Official transcript processing',
            department=self.department
        )
        self.requirement = ServiceRequirement.objects.create(
            service=self.service,
            name='Valid ID',
            description='Government-issued ID required',
            is_required=True
        )
    
    def test_appointment_creation(self):
        """Test appointment creation"""
        appointment = Appointment.objects.create(
            student=self.user,
            service=self.service
        )

        self.assertEqual(appointment.student, self.user)
        self.assertEqual(appointment.service, self.service)
        self.assertEqual(appointment.status, 'pending')
        self.assertIsNotNone(appointment.appointment_id)
    
    def test_appointment_id_generation(self):
        """Test unique appointment ID generation"""
        appointment1 = Appointment.objects.create(
            student=self.user,
            service=self.service
        )
        appointment2 = Appointment.objects.create(
            student=self.user,
            service=self.service
        )
        
        self.assertNotEqual(appointment1.appointment_id, appointment2.appointment_id)
        self.assertTrue(appointment1.appointment_id.startswith('APP'))
    
    def test_appointment_requirements_creation(self):
        """Test automatic requirement creation"""
        appointment = Appointment.objects.create(
            student=self.user,
            service=self.service
        )
        
        # Check that appointment requirements were created
        req_count = AppointmentRequirement.objects.filter(
            appointment=appointment
        ).count()
        self.assertEqual(req_count, 1)
        
        app_req = AppointmentRequirement.objects.get(
            appointment=appointment,
            requirement=self.requirement
        )
        self.assertFalse(app_req.is_completed)
    
    def test_appointment_str_method(self):
        """Test appointment string representation"""
        appointment = Appointment.objects.create(
            student=self.user,
            service=self.service
        )
        expected = f"{appointment.appointment_id} - {self.user.get_full_name()} - {self.service.name}"
        self.assertEqual(str(appointment), expected)
    
    def test_qr_code_generation(self):
        """Test QR code generation"""
        appointment = Appointment.objects.create(
            student=self.user,
            service=self.service
        )

        # Mark all requirements as completed first
        for req in appointment.requirements.all():
            req.is_completed = True
            req.save()

        qr_base64 = appointment.generate_qr_code()

        # Verify QR code was generated
        self.assertIsNotNone(qr_base64)
        self.assertIsInstance(qr_base64, str)

        # Verify appointment status was updated
        appointment.refresh_from_db()
        self.assertEqual(appointment.status, 'ready')
        self.assertIsNotNone(appointment.qr_hash)
    
    def test_qr_code_validation(self):
        """Test QR code validation"""
        appointment = Appointment.objects.create(
            student=self.user,
            service=self.service
        )

        # Mark all requirements as completed first
        for req in appointment.requirements.all():
            req.is_completed = True
            req.save()

        # Generate QR code
        appointment.generate_qr_code()

        # Create valid QR data for testing
        qr_data = {
            'appointment_id': appointment.appointment_id,
            'student_id': str(self.user.id),
            'hash': appointment.qr_hash,
            'version': '1.0'
        }
        qr_json = json.dumps(qr_data)

        # Test valid QR code
        is_valid, message = appointment.validate_qr_code(qr_json)
        self.assertTrue(is_valid)

        # Test invalid QR code (tampered data)
        tampered_data = qr_data.copy()
        tampered_data['student_id'] = 999
        tampered_json = json.dumps(tampered_data)
        
        is_valid, message = appointment.validate_qr_code(tampered_json)
        self.assertFalse(is_valid)


class AuditLogModelTest(TestCase):
    """Test AuditLog model functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_audit_log_creation(self):
        """Test audit log creation"""
        log = AuditLog.objects.create(
            user=self.user,
            action='login',
            description='User logged in successfully',
            ip_address='***********'
        )
        
        self.assertEqual(log.user, self.user)
        self.assertEqual(log.action, 'login')
        self.assertIsNotNone(log.timestamp)
    
    def test_audit_log_str_method(self):
        """Test audit log string representation"""
        log = AuditLog.objects.create(
            user=self.user,
            action='login',
            description='User logged in'
        )
        
        expected = f"{log.timestamp.strftime('%Y-%m-%d %H:%M:%S')} - {self.user.username} - User Login"
        self.assertEqual(str(log), expected)
    
    def test_get_client_ip_helper(self):
        """Test client IP extraction helper"""
        # Mock request with X-Forwarded-For header
        class MockRequest:
            META = {'HTTP_X_FORWARDED_FOR': '***********, ***********'}
        
        request = MockRequest()
        ip = AuditLog.get_client_ip(request)
        self.assertEqual(ip, '***********')
        
        # Mock request with REMOTE_ADDR
        class MockRequest2:
            META = {'REMOTE_ADDR': '***********00'}
        
        request2 = MockRequest2()
        ip2 = AuditLog.get_client_ip(request2)
        self.assertEqual(ip2, '***********00')
    
    def test_log_action_class_method(self):
        """Test AuditLog.log_action class method"""
        # Mock request
        class MockSession:
            session_key = 'test_session'

        class MockRequest:
            META = {'REMOTE_ADDR': '***********', 'HTTP_USER_AGENT': 'Test Browser'}
            session = MockSession()
            path = '/test/'
            method = 'GET'
        
        request = MockRequest()
        
        log = AuditLog.log_action(
            user=self.user,
            action='test_action',
            description='Test description',
            request=request
        )
        
        self.assertEqual(log.user, self.user)
        self.assertEqual(log.action, 'test_action')
        self.assertEqual(log.ip_address, '***********')
        self.assertEqual(log.request_path, '/test/')
        self.assertEqual(log.request_method, 'GET')


class SecurityEventModelTest(TestCase):
    """Test SecurityEvent model functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_security_event_creation(self):
        """Test security event creation"""
        event = SecurityEvent.objects.create(
            event_type='failed_login',
            severity='medium',
            user=self.user,
            ip_address='***********',
            description='Failed login attempt'
        )
        
        self.assertEqual(event.event_type, 'failed_login')
        self.assertEqual(event.severity, 'medium')
        self.assertFalse(event.resolved)
        self.assertIsNotNone(event.timestamp)
    
    def test_security_event_str_method(self):
        """Test security event string representation"""
        event = SecurityEvent.objects.create(
            event_type='failed_login',
            severity='high',
            ip_address='***********',
            description='Multiple failed attempts'
        )

        expected = f"{event.timestamp.strftime('%Y-%m-%d %H:%M:%S')} - Failed Login (High)"
        self.assertEqual(str(event), expected)


class FileAccessLogModelTest(TestCase):
    """Test FileAccessLog model functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.department = Department.objects.create(
            name='Registrar',
            code='REG',
            description='Student records'
        )
        self.service = Service.objects.create(
            name='Transcript Request',
            description='Official transcript processing',
            department=self.department,
            processing_time='5 business days'
        )
        self.appointment = Appointment.objects.create(
            student=self.user,
            service=self.service
        )
    
    def test_file_access_log_creation(self):
        """Test file access log creation"""
        log = FileAccessLog.objects.create(
            user=self.user,
            appointment=self.appointment,
            file_path='/media/uploads/test.pdf',
            file_name='test.pdf',
            access_type='upload',
            ip_address='***********',
            success=True,
            file_size=1024
        )
        
        self.assertEqual(log.user, self.user)
        self.assertEqual(log.appointment, self.appointment)
        self.assertEqual(log.access_type, 'upload')
        self.assertTrue(log.success)
        self.assertEqual(log.file_size, 1024)
    
    def test_file_access_log_str_method(self):
        """Test file access log string representation"""
        log = FileAccessLog.objects.create(
            user=self.user,
            file_path='/media/uploads/document.pdf',
            file_name='document.pdf',
            access_type='download',
            ip_address='***********'
        )

        expected = f"{log.timestamp.strftime('%Y-%m-%d %H:%M:%S')} - {self.user.username} - File Download - document.pdf"
        self.assertEqual(str(log), expected)
