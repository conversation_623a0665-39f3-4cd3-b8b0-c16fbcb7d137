/**
 * Appointment Management System with Modal Integration
 * Handles all appointment-related AJAX operations with modal feedback
 */

class AppointmentManager {
    constructor() {
        this.csrfToken = this.getCSRFToken();
        this.init();
    }

    init() {
        // Bind event listeners when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.bindEvents());
        } else {
            this.bindEvents();
        }
    }

    bindEvents() {
        // Status update buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="update-status"]')) {
                e.preventDefault();
                this.handleStatusUpdate(e.target);
            }
            
            if (e.target.matches('[data-action="bulk-update"]')) {
                e.preventDefault();
                this.handleBulkUpdate(e.target);
            }
            
            if (e.target.matches('[data-action="assign-staff"]')) {
                e.preventDefault();
                this.handleStaffAssignment(e.target);
            }
            
            if (e.target.matches('[data-action="add-note"]')) {
                e.preventDefault();
                this.handleAddNote(e.target);
            }
            
            if (e.target.matches('[data-action="view-history"]')) {
                e.preventDefault();
                this.handleViewHistory(e.target);
            }
        });

        // Bulk selection
        document.addEventListener('change', (e) => {
            if (e.target.matches('[data-bulk-select]')) {
                this.updateBulkActions();
            }
            
            if (e.target.matches('#select-all-appointments')) {
                this.handleSelectAll(e.target);
            }
        });

        // Auto-save for forms
        document.addEventListener('input', (e) => {
            if (e.target.matches('[data-auto-save]')) {
                this.debounce(() => this.autoSave(e.target), 1000)();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                this.handleKeyboardShortcuts(e);
            }
        });
    }

    getCSRFToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }

    // Status Update Methods
    handleStatusUpdate(element) {
        const appointmentId = element.dataset.appointmentId;
        const newStatus = element.dataset.newStatus;
        const currentStatus = element.dataset.currentStatus;

        const statusLabels = {
            'pending': 'Pending',
            'in_progress': 'In Progress',
            'ready': 'Ready for Pickup',
            'claimed': 'Completed'
        };

        showConfirmationModal(
            'Update Appointment Status',
            `Change status from "${statusLabels[currentStatus]}" to "${statusLabels[newStatus]}"?`,
            () => this.updateAppointmentStatus(appointmentId, newStatus),
            'Update Status'
        );
    }

    async updateAppointmentStatus(appointmentId, newStatus) {
        showLoadingModal('Updating Status', 'Please wait while we update the appointment status...');

        try {
            const response = await fetch(`/staff/ajax/appointments/${appointmentId}/update-status/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.csrfToken,
                },
                body: JSON.stringify({ status: newStatus })
            });

            const data = await response.json();

            if (data.success) {
                showSuccessModal(
                    'Status Updated',
                    data.message || 'Appointment status has been updated successfully.',
                    () => window.location.reload()
                );
            } else {
                showErrorModal('Update Failed', data.message || 'Failed to update appointment status.');
            }
        } catch (error) {
            console.error('Status update error:', error);
            showErrorModal('Network Error', 'Unable to connect to the server. Please try again.');
        }
    }

    // Bulk Operations
    handleBulkUpdate(element) {
        const selectedAppointments = this.getSelectedAppointments();
        if (selectedAppointments.length === 0) {
            showErrorModal('No Selection', 'Please select at least one appointment to update.');
            return;
        }

        const action = element.dataset.bulkAction;
        const actionLabels = {
            'mark_in_progress': 'Mark as In Progress',
            'mark_ready': 'Mark as Ready',
            'mark_completed': 'Mark as Completed',
            'assign_staff': 'Assign Staff Member'
        };

        showConfirmationModal(
            'Bulk Update',
            `${actionLabels[action]} for ${selectedAppointments.length} selected appointment(s)?`,
            () => this.performBulkUpdate(selectedAppointments, action),
            'Update All'
        );
    }

    async performBulkUpdate(appointmentIds, action) {
        showLoadingModal('Processing Bulk Update', `Updating ${appointmentIds.length} appointments...`);

        try {
            const response = await fetch('/staff/ajax/appointments/bulk-update/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.csrfToken,
                },
                body: JSON.stringify({
                    appointment_ids: appointmentIds,
                    action: action
                })
            });

            const data = await response.json();

            if (data.success) {
                showSuccessModal(
                    'Bulk Update Complete',
                    `Successfully updated ${data.updated_count} appointment(s).`,
                    () => window.location.reload()
                );
            } else {
                showErrorModal('Bulk Update Failed', data.message || 'Some appointments could not be updated.');
            }
        } catch (error) {
            console.error('Bulk update error:', error);
            showErrorModal('Network Error', 'Unable to process bulk update. Please try again.');
        }
    }

    // Staff Assignment
    handleStaffAssignment(element) {
        const appointmentId = element.dataset.appointmentId;
        this.showStaffAssignmentModal(appointmentId);
    }

    async showStaffAssignmentModal(appointmentId) {
        try {
            const response = await fetch('/staff/ajax/get-staff-list/');
            const data = await response.json();

            if (data.success) {
                const staffOptions = data.staff.map(staff => 
                    `<option value="${staff.id}">${staff.name} - ${staff.department}</option>`
                ).join('');

                const formContent = `
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Assign to Staff Member</label>
                            <select id="staff-select" class="w-full rounded-xl border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500">
                                <option value="">Select staff member...</option>
                                ${staffOptions}
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Assignment Note (Optional)</label>
                            <textarea id="assignment-note" rows="3" class="w-full rounded-xl border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500" placeholder="Add any special instructions..."></textarea>
                        </div>
                    </div>
                `;

                showFormModal(
                    'Assign Staff Member',
                    formContent,
                    (modalContent) => this.processStaffAssignment(appointmentId, modalContent),
                    'Assign'
                );
            }
        } catch (error) {
            showErrorModal('Error', 'Unable to load staff list. Please try again.');
        }
    }

    async processStaffAssignment(appointmentId, modalContent) {
        const staffId = modalContent.querySelector('#staff-select').value;
        const note = modalContent.querySelector('#assignment-note').value;

        if (!staffId) {
            showErrorModal('Invalid Selection', 'Please select a staff member to assign.');
            return;
        }

        showLoadingModal('Assigning Staff', 'Processing staff assignment...');

        try {
            const response = await fetch(`/staff/ajax/appointments/${appointmentId}/assign/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.csrfToken,
                },
                body: JSON.stringify({
                    staff_id: staffId,
                    note: note
                })
            });

            const data = await response.json();

            if (data.success) {
                showSuccessModal(
                    'Staff Assigned',
                    'Staff member has been assigned to this appointment.',
                    () => window.location.reload()
                );
            } else {
                showErrorModal('Assignment Failed', data.message || 'Failed to assign staff member.');
            }
        } catch (error) {
            showErrorModal('Network Error', 'Unable to process assignment. Please try again.');
        }
    }

    // Notes Management
    handleAddNote(element) {
        const appointmentId = element.dataset.appointmentId;
        this.showAddNoteModal(appointmentId);
    }

    showAddNoteModal(appointmentId) {
        const formContent = `
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Internal Note</label>
                    <textarea id="note-content" rows="4" class="w-full rounded-xl border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500" placeholder="Add your note here..." required></textarea>
                </div>
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" id="note-important" class="rounded border-gray-300 text-emerald-600 shadow-sm focus:border-emerald-300 focus:ring focus:ring-emerald-200 focus:ring-opacity-50">
                        <span class="ml-2 text-sm text-gray-700">Mark as important</span>
                    </label>
                </div>
            </div>
        `;

        showFormModal(
            'Add Internal Note',
            formContent,
            (modalContent) => this.processAddNote(appointmentId, modalContent),
            'Add Note'
        );
    }

    async processAddNote(appointmentId, modalContent) {
        const content = modalContent.querySelector('#note-content').value.trim();
        const important = modalContent.querySelector('#note-important').checked;

        if (!content) {
            showErrorModal('Invalid Input', 'Please enter a note before submitting.');
            return;
        }

        showLoadingModal('Adding Note', 'Saving your note...');

        try {
            const response = await fetch(`/staff/ajax/appointments/${appointmentId}/add-note/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.csrfToken,
                },
                body: JSON.stringify({
                    content: content,
                    important: important
                })
            });

            const data = await response.json();

            if (data.success) {
                showSuccessModal(
                    'Note Added',
                    'Your note has been added to the appointment.',
                    () => this.refreshAppointmentData(appointmentId)
                );
            } else {
                showErrorModal('Failed to Add Note', data.message || 'Unable to save the note.');
            }
        } catch (error) {
            showErrorModal('Network Error', 'Unable to save note. Please try again.');
        }
    }

    // Utility Methods
    getSelectedAppointments() {
        const checkboxes = document.querySelectorAll('[data-bulk-select]:checked');
        return Array.from(checkboxes).map(cb => cb.value);
    }

    updateBulkActions() {
        const selectedCount = this.getSelectedAppointments().length;
        const bulkActions = document.querySelector('#bulk-actions');
        
        if (bulkActions) {
            if (selectedCount > 0) {
                bulkActions.classList.remove('hidden');
                bulkActions.querySelector('.selected-count').textContent = selectedCount;
            } else {
                bulkActions.classList.add('hidden');
            }
        }
    }

    handleSelectAll(checkbox) {
        const bulkCheckboxes = document.querySelectorAll('[data-bulk-select]');
        bulkCheckboxes.forEach(cb => cb.checked = checkbox.checked);
        this.updateBulkActions();
    }

    refreshAppointmentData(appointmentId) {
        // Refresh specific appointment data or reload page
        if (window.location.pathname.includes('/appointments/')) {
            window.location.reload();
        } else {
            // Update specific appointment row if on list page
            this.updateAppointmentRow(appointmentId);
        }
    }

    async updateAppointmentRow(appointmentId) {
        try {
            const response = await fetch(`/staff/ajax/appointments/${appointmentId}/data/`);
            const data = await response.json();
            
            if (data.success) {
                const row = document.querySelector(`[data-appointment-id="${appointmentId}"]`);
                if (row) {
                    // Update row content with new data
                    this.updateRowContent(row, data.appointment);
                }
            }
        } catch (error) {
            console.error('Failed to refresh appointment data:', error);
        }
    }

    updateRowContent(row, appointmentData) {
        // Update status badge
        const statusBadge = row.querySelector('.status-badge');
        if (statusBadge) {
            statusBadge.className = `status-badge inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full ${this.getStatusClasses(appointmentData.status)}`;
            statusBadge.innerHTML = `${this.getStatusIcon(appointmentData.status)} ${appointmentData.status_display}`;
        }

        // Update action buttons
        const actionButtons = row.querySelector('.action-buttons');
        if (actionButtons) {
            actionButtons.innerHTML = this.generateActionButtons(appointmentData);
        }
    }

    getStatusClasses(status) {
        const classes = {
            'pending': 'bg-gradient-to-r from-gold-100 to-gold-200 text-gold-800',
            'in_progress': 'bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800',
            'ready': 'bg-gradient-to-r from-emerald-100 to-emerald-200 text-emerald-800',
            'claimed': 'bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800'
        };
        return classes[status] || 'bg-gray-100 text-gray-800';
    }

    getStatusIcon(status) {
        const icons = {
            'pending': '<svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>',
            'in_progress': '<svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" /></svg>',
            'ready': '<svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>',
            'claimed': '<svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>'
        };
        return icons[status] || '';
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    handleKeyboardShortcuts(e) {
        // Ctrl+S or Cmd+S for save
        if (e.key === 's') {
            e.preventDefault();
            const activeForm = document.querySelector('form:focus-within');
            if (activeForm) {
                this.autoSave(activeForm);
            }
        }
        
        // Ctrl+A or Cmd+A for select all (in appointment list)
        if (e.key === 'a' && document.querySelector('#select-all-appointments')) {
            e.preventDefault();
            document.querySelector('#select-all-appointments').click();
        }
    }

    async autoSave(element) {
        const form = element.closest('form');
        if (!form) return;

        const formData = new FormData(form);
        const appointmentId = form.dataset.appointmentId;

        try {
            const response = await fetch(`/staff/appointments/${appointmentId}/auto-save/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.csrfToken,
                },
                body: formData
            });

            const data = await response.json();
            
            if (data.success) {
                // Show subtle save indicator
                this.showAutoSaveIndicator(true);
            } else {
                this.showAutoSaveIndicator(false);
            }
        } catch (error) {
            this.showAutoSaveIndicator(false);
        }
    }

    showAutoSaveIndicator(success) {
        const indicator = document.querySelector('#auto-save-indicator');
        if (indicator) {
            indicator.textContent = success ? 'Saved' : 'Save failed';
            indicator.className = success ? 'text-emerald-600' : 'text-red-600';
            
            setTimeout(() => {
                indicator.textContent = '';
            }, 2000);
        }
    }
}

// Initialize appointment manager
let appointmentManager = null;

document.addEventListener('DOMContentLoaded', function() {
    appointmentManager = new AppointmentManager();
});
