"""
Test cases for appointment views
"""

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from django.utils import timezone
import json

from appointments.models import (
    UserProfile, Department, Service, ServiceRequirement,
    Appointment, AppointmentRequirement, AuditLog, SecurityEvent
)


class BaseViewTest(TestCase):
    """Base test class with common setup"""
    
    def setUp(self):
        self.client = Client()
        
        # Create test users
        self.student_user = User.objects.create_user(
            username='student1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.student_user.userprofile.role = 'student'
        self.student_user.userprofile.save()
        
        self.staff_user = User.objects.create_user(
            username='staff1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.staff_user.userprofile.role = 'office_staff'
        self.staff_user.userprofile.save()
        
        self.admin_user = User.objects.create_user(
            username='admin1',
            email='<EMAIL>',
            password='testpass123'
        )
        self.admin_user.userprofile.role = 'admin'
        self.admin_user.userprofile.save()
        
        # Create test department and service
        self.department = Department.objects.create(
            name='Registrar',
            description='Student records and enrollment'
        )
        self.staff_user.userprofile.department = self.department
        self.staff_user.userprofile.save()
        
        self.service = Service.objects.create(
            name='Transcript Request',
            description='Official transcript processing',
            department=self.department
        )
        
        self.requirement = ServiceRequirement.objects.create(
            service=self.service,
            name='Valid ID',
            description='Government-issued ID required',
            is_required=True
        )


class AuthenticationViewTest(BaseViewTest):
    """Test authentication and access control"""
    
    def test_login_view(self):
        """Test login functionality"""
        response = self.client.get(reverse('login'))
        self.assertEqual(response.status_code, 200)
        
        # Test successful login
        response = self.client.post(reverse('login'), {
            'username': 'student1',
            'password': 'testpass123'
        })
        self.assertEqual(response.status_code, 302)  # Redirect after login
    
    def test_logout_view(self):
        """Test logout functionality"""
        self.client.login(username='student1', password='testpass123')
        response = self.client.post(reverse('logout'))
        self.assertEqual(response.status_code, 302)  # Redirect after logout
    
    def test_role_based_access(self):
        """Test role-based access control"""
        # Test student access to admin page (should be denied)
        self.client.login(username='student1', password='testpass123')
        response = self.client.get(reverse('admin_dashboard'))
        self.assertEqual(response.status_code, 403)
        
        # Test admin access to admin page (should be allowed)
        self.client.login(username='admin1', password='testpass123')
        response = self.client.get(reverse('admin_dashboard'))
        self.assertEqual(response.status_code, 200)


class StudentViewTest(BaseViewTest):
    """Test student-specific views"""
    
    def setUp(self):
        super().setUp()
        self.client.login(username='student1', password='testpass123')
    
    def test_student_dashboard(self):
        """Test student dashboard view"""
        response = self.client.get(reverse('student_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Student Dashboard')
    
    def test_create_appointment(self):
        """Test appointment creation"""
        response = self.client.get(reverse('create_appointment'))
        self.assertEqual(response.status_code, 200)
        
        # Test appointment creation
        response = self.client.post(reverse('create_appointment'), {
            'service': self.service.id,
            'purpose': 'Need transcript for job application',
            'preferred_date': timezone.now().date(),
            'preferred_time': '10:00'
        })
        self.assertEqual(response.status_code, 302)  # Redirect after creation
        
        # Verify appointment was created
        appointment = Appointment.objects.filter(student=self.student_user).first()
        self.assertIsNotNone(appointment)
        self.assertEqual(appointment.service, self.service)
    
    def test_appointment_list(self):
        """Test student appointment list"""
        # Create test appointment
        appointment = Appointment.objects.create(
            student=self.student_user,
            service=self.service,
            purpose='Test appointment'
        )
        
        response = self.client.get(reverse('student_appointments'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, appointment.appointment_id)
    
    def test_appointment_detail(self):
        """Test appointment detail view"""
        appointment = Appointment.objects.create(
            student=self.student_user,
            service=self.service,
            purpose='Test appointment'
        )
        
        response = self.client.get(
            reverse('appointment_detail', kwargs={'appointment_id': appointment.appointment_id})
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, appointment.appointment_id)
    
    def test_file_upload(self):
        """Test file upload functionality"""
        appointment = Appointment.objects.create(
            student=self.student_user,
            service=self.service
        )
        
        # Create test file
        test_file = SimpleUploadedFile(
            "test.pdf",
            b"file_content",
            content_type="application/pdf"
        )
        
        response = self.client.post(reverse('upload_file'), {
            'appointment_id': appointment.appointment_id,
            'requirement_id': self.requirement.id,
            'file': test_file
        })
        
        self.assertEqual(response.status_code, 200)
        
        # Verify requirement was marked as completed
        app_req = AppointmentRequirement.objects.get(
            appointment=appointment,
            requirement=self.requirement
        )
        self.assertTrue(app_req.is_completed)
    
    def test_qr_code_generation(self):
        """Test QR code generation"""
        appointment = Appointment.objects.create(
            student=self.student_user,
            service=self.service,
            status='ready'
        )
        
        response = self.client.get(
            reverse('generate_qr', kwargs={'appointment_id': appointment.appointment_id})
        )
        self.assertEqual(response.status_code, 200)
        
        # Verify QR code data
        data = json.loads(response.content)
        self.assertIn('qr_code', data)
        self.assertIn('qr_data', data)


class StaffViewTest(BaseViewTest):
    """Test office staff views"""
    
    def setUp(self):
        super().setUp()
        self.client.login(username='staff1', password='testpass123')
        
        # Create test appointment
        self.appointment = Appointment.objects.create(
            student=self.student_user,
            service=self.service,
            purpose='Test appointment'
        )
    
    def test_staff_dashboard(self):
        """Test staff dashboard view"""
        response = self.client.get(reverse('staff_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Office Staff Dashboard')
    
    def test_staff_appointment_list(self):
        """Test staff appointment list"""
        response = self.client.get(reverse('staff_appointments'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.appointment.appointment_id)
    
    def test_appointment_review(self):
        """Test appointment review functionality"""
        response = self.client.get(
            reverse('staff_appointment_detail', kwargs={'appointment_id': self.appointment.appointment_id})
        )
        self.assertEqual(response.status_code, 200)
        
        # Test status update
        response = self.client.post(
            reverse('update_appointment_status'),
            {
                'appointment_id': self.appointment.appointment_id,
                'status': 'approved',
                'notes': 'All requirements met'
            }
        )
        self.assertEqual(response.status_code, 200)
        
        # Verify status was updated
        self.appointment.refresh_from_db()
        self.assertEqual(self.appointment.status, 'approved')
    
    def test_qr_code_validation(self):
        """Test QR code validation by staff"""
        # Generate QR code first
        qr_data, _ = self.appointment.generate_qr_code()
        qr_json = json.dumps(qr_data)
        
        response = self.client.post(reverse('validate_qr'), {
            'qr_data': qr_json
        })
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertTrue(data['valid'])
    
    def test_requirement_management(self):
        """Test requirement completion management"""
        app_req = AppointmentRequirement.objects.get(
            appointment=self.appointment,
            requirement=self.requirement
        )
        
        response = self.client.post(reverse('toggle_requirement'), {
            'requirement_id': app_req.id,
            'completed': 'true'
        })
        
        self.assertEqual(response.status_code, 200)
        
        # Verify requirement was updated
        app_req.refresh_from_db()
        self.assertTrue(app_req.is_completed)


class AdminViewTest(BaseViewTest):
    """Test administrator views"""
    
    def setUp(self):
        super().setUp()
        self.client.login(username='admin1', password='testpass123')
    
    def test_admin_dashboard(self):
        """Test admin dashboard view"""
        response = self.client.get(reverse('admin_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Administrator Dashboard')
    
    def test_admin_appointment_list(self):
        """Test admin appointment management"""
        appointment = Appointment.objects.create(
            student=self.student_user,
            service=self.service
        )
        
        response = self.client.get(reverse('admin_appointment_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, appointment.appointment_id)
    
    def test_admin_user_management(self):
        """Test admin user management"""
        response = self.client.get(reverse('admin_user_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.student_user.username)
    
    def test_admin_department_management(self):
        """Test admin department management"""
        response = self.client.get(reverse('admin_department_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.department.name)
    
    def test_admin_reports(self):
        """Test admin reports view"""
        response = self.client.get(reverse('admin_reports'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'System Reports')
    
    def test_security_dashboard(self):
        """Test security dashboard"""
        # Create test security event
        SecurityEvent.objects.create(
            event_type='failed_login',
            severity='medium',
            description='Test security event'
        )
        
        response = self.client.get(reverse('admin_security_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Security Dashboard')
    
    def test_audit_logs(self):
        """Test audit logs view"""
        # Create test audit log
        AuditLog.objects.create(
            user=self.student_user,
            action='login',
            description='User logged in'
        )
        
        response = self.client.get(reverse('admin_audit_logs'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Audit Logs')


class SecurityViewTest(BaseViewTest):
    """Test security-related functionality"""
    
    def test_failed_login_logging(self):
        """Test failed login attempt logging"""
        initial_count = AuditLog.objects.filter(action='login_failed').count()
        
        # Attempt login with wrong password
        response = self.client.post(reverse('login'), {
            'username': 'student1',
            'password': 'wrongpassword'
        })
        
        # Verify failed login was logged
        final_count = AuditLog.objects.filter(action='login_failed').count()
        self.assertEqual(final_count, initial_count + 1)
    
    def test_file_upload_security(self):
        """Test file upload security validation"""
        appointment = Appointment.objects.create(
            student=self.student_user,
            service=self.service
        )
        
        self.client.login(username='student1', password='testpass123')
        
        # Test malicious file upload
        malicious_file = SimpleUploadedFile(
            "malicious.exe",
            b"malicious_content",
            content_type="application/x-executable"
        )
        
        response = self.client.post(reverse('upload_file'), {
            'appointment_id': appointment.appointment_id,
            'requirement_id': self.requirement.id,
            'file': malicious_file
        })
        
        # Should reject malicious file
        self.assertEqual(response.status_code, 400)
    
    def test_qr_code_tampering_detection(self):
        """Test QR code tampering detection"""
        appointment = Appointment.objects.create(
            student=self.student_user,
            service=self.service
        )
        
        self.client.login(username='staff1', password='testpass123')
        
        # Create tampered QR data
        tampered_data = {
            'appointment_id': appointment.appointment_id,
            'student_id': 999,  # Wrong student ID
            'service': self.service.name,
            'hash': 'invalid_hash'
        }
        
        response = self.client.post(reverse('validate_qr'), {
            'qr_data': json.dumps(tampered_data)
        })
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertFalse(data['valid'])
        self.assertIn('tampered', data['message'].lower())
