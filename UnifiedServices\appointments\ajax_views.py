"""
AJAX Views for Staff Appointment Management
Handles all AJAX requests with JSON responses for modal integration
"""

import json
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db import transaction
from django.core.exceptions import ValidationError

from .models import Appointment, AppointmentNote, UserProfile
from .decorators import staff_required


@login_required
@staff_required
@require_http_methods(["POST"])
def update_appointment_status(request, appointment_id):
    """Update appointment status via AJAX"""
    try:
        data = json.loads(request.body)
        new_status = data.get('status')
        
        if new_status not in ['pending', 'in_progress', 'ready', 'claimed']:
            return JsonResponse({
                'success': False,
                'message': 'Invalid status provided.'
            })
        
        appointment = get_object_or_404(Appointment, appointment_id=appointment_id)
        
        # Check if staff member can update this appointment
        if not can_staff_update_appointment(request.user, appointment):
            return JsonResponse({
                'success': False,
                'message': 'You do not have permission to update this appointment.'
            })
        
        old_status = appointment.status
        appointment.status = new_status
        appointment.updated_at = timezone.now()
        appointment.save()
        
        # Create audit log
        create_appointment_log(appointment, request.user, f'Status changed from {old_status} to {new_status}')
        
        return JsonResponse({
            'success': True,
            'message': f'Appointment status updated to {appointment.get_status_display()}.',
            'appointment': {
                'id': appointment.appointment_id,
                'status': appointment.status,
                'status_display': appointment.get_status_display(),
                'updated_at': appointment.updated_at.isoformat()
            }
        })
        
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': 'Invalid JSON data provided.'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'An error occurred: {str(e)}'
        })


@login_required
@staff_required
@require_http_methods(["POST"])
def bulk_update_appointments(request):
    """Handle bulk appointment updates"""
    try:
        data = json.loads(request.body)
        appointment_ids = data.get('appointment_ids', [])
        action = data.get('action')
        
        if not appointment_ids:
            return JsonResponse({
                'success': False,
                'message': 'No appointments selected.'
            })
        
        appointments = Appointment.objects.filter(appointment_id__in=appointment_ids)
        
        # Filter appointments that the staff member can update
        updatable_appointments = [
            apt for apt in appointments 
            if can_staff_update_appointment(request.user, apt)
        ]
        
        if not updatable_appointments:
            return JsonResponse({
                'success': False,
                'message': 'You do not have permission to update the selected appointments.'
            })
        
        updated_count = 0
        
        with transaction.atomic():
            for appointment in updatable_appointments:
                old_status = appointment.status
                
                if action == 'mark_in_progress':
                    appointment.status = 'in_progress'
                elif action == 'mark_ready':
                    appointment.status = 'ready'
                elif action == 'mark_completed':
                    appointment.status = 'claimed'
                else:
                    continue
                
                appointment.updated_at = timezone.now()
                appointment.save()
                
                # Create audit log
                create_appointment_log(
                    appointment, 
                    request.user, 
                    f'Bulk update: Status changed from {old_status} to {appointment.status}'
                )
                
                updated_count += 1
        
        return JsonResponse({
            'success': True,
            'message': f'Successfully updated {updated_count} appointment(s).',
            'updated_count': updated_count
        })
        
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': 'Invalid JSON data provided.'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'An error occurred: {str(e)}'
        })


@login_required
@staff_required
@require_http_methods(["GET"])
def get_staff_list(request):
    """Get list of staff members for assignment"""
    try:
        # Get staff members from the same department or all if admin
        user_profile = request.user.userprofile
        
        if user_profile.role == 'admin':
            staff_profiles = UserProfile.objects.filter(
                role__in=['office_staff', 'admin']
            ).select_related('user', 'department')
        else:
            staff_profiles = UserProfile.objects.filter(
                department=user_profile.department,
                role__in=['office_staff', 'admin']
            ).select_related('user', 'department')
        
        staff_list = []
        for profile in staff_profiles:
            staff_list.append({
                'id': profile.user.id,
                'name': profile.user.get_full_name() or profile.user.username,
                'department': profile.department.name if profile.department else 'No Department'
            })
        
        return JsonResponse({
            'success': True,
            'staff': staff_list
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'An error occurred: {str(e)}'
        })


@login_required
@staff_required
@require_http_methods(["POST"])
def assign_staff_to_appointment(request, appointment_id):
    """Assign staff member to appointment"""
    try:
        data = json.loads(request.body)
        staff_id = data.get('staff_id')
        note = data.get('note', '')
        
        appointment = get_object_or_404(Appointment, appointment_id=appointment_id)
        staff_user = get_object_or_404(User, id=staff_id)
        
        # Check permissions
        if not can_staff_update_appointment(request.user, appointment):
            return JsonResponse({
                'success': False,
                'message': 'You do not have permission to assign staff to this appointment.'
            })
        
        # Assign staff
        appointment.assigned_staff = staff_user
        appointment.updated_at = timezone.now()
        appointment.save()
        
        # Create audit log
        create_appointment_log(
            appointment, 
            request.user, 
            f'Assigned to {staff_user.get_full_name() or staff_user.username}'
        )
        
        # Add note if provided
        if note:
            AppointmentNote.objects.create(
                appointment=appointment,
                staff_member=request.user,
                content=note,
                note_type='assignment'
            )
        
        return JsonResponse({
            'success': True,
            'message': f'Appointment assigned to {staff_user.get_full_name() or staff_user.username}.'
        })
        
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': 'Invalid JSON data provided.'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'An error occurred: {str(e)}'
        })


@login_required
@staff_required
@require_http_methods(["POST"])
def add_appointment_note(request, appointment_id):
    """Add internal note to appointment"""
    try:
        data = json.loads(request.body)
        content = data.get('content', '').strip()
        important = data.get('important', False)
        
        if not content:
            return JsonResponse({
                'success': False,
                'message': 'Note content cannot be empty.'
            })
        
        appointment = get_object_or_404(Appointment, appointment_id=appointment_id)
        
        # Check permissions
        if not can_staff_update_appointment(request.user, appointment):
            return JsonResponse({
                'success': False,
                'message': 'You do not have permission to add notes to this appointment.'
            })
        
        # Create note
        note = AppointmentNote.objects.create(
            appointment=appointment,
            staff_member=request.user,
            content=content,
            important=important,
            note_type='internal'
        )
        
        # Create audit log
        create_appointment_log(
            appointment, 
            request.user, 
            f'Added {"important " if important else ""}internal note'
        )
        
        return JsonResponse({
            'success': True,
            'message': 'Note added successfully.',
            'note': {
                'id': note.id,
                'content': note.content,
                'important': note.important,
                'created_at': note.created_at.isoformat(),
                'staff_member': note.staff_member.get_full_name() or note.staff_member.username
            }
        })
        
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': 'Invalid JSON data provided.'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'An error occurred: {str(e)}'
        })


@login_required
@staff_required
@require_http_methods(["GET"])
def get_appointment_data(request, appointment_id):
    """Get updated appointment data for refreshing UI"""
    try:
        appointment = get_object_or_404(Appointment, appointment_id=appointment_id)
        
        return JsonResponse({
            'success': True,
            'appointment': {
                'id': appointment.appointment_id,
                'status': appointment.status,
                'status_display': appointment.get_status_display(),
                'student_name': appointment.student.get_full_name(),
                'service_name': appointment.service.name,
                'created_at': appointment.created_at.isoformat(),
                'updated_at': appointment.updated_at.isoformat(),
                'assigned_staff': appointment.assigned_staff.get_full_name() if appointment.assigned_staff else None,
                'completion_percentage': getattr(appointment, 'completion_percentage', 0),
                'completed_requirements_count': getattr(appointment, 'completed_requirements_count', 0),
                'total_requirements': appointment.requirements.count() if hasattr(appointment, 'requirements') else 0
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'An error occurred: {str(e)}'
        })


# Helper Functions

def can_staff_update_appointment(user, appointment):
    """Check if staff member can update the appointment"""
    try:
        user_profile = user.userprofile
        
        # Admins can update any appointment
        if user_profile.role == 'admin':
            return True
        
        # Staff can update appointments in their department
        if user_profile.role == 'office_staff':
            return appointment.service.department == user_profile.department
        
        return False
        
    except (AttributeError, UserProfile.DoesNotExist):
        return False


def create_appointment_log(appointment, user, action):
    """Create audit log entry for appointment"""
    try:
        # This would create an audit log entry
        # Implementation depends on your audit log model
        pass
    except Exception:
        # Log creation failure shouldn't break the main operation
        pass
