/**
 * Modal System for Staff Appointment Management
 * Provides comprehensive modal functionality with emerald/gold theme
 */

class ModalSystem {
    constructor() {
        this.overlay = null;
        this.content = null;
        this.currentModal = null;
        this.focusedElementBeforeModal = null;
        this.init();
    }

    init() {
        // Ensure DOM is loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupModal());
        } else {
            this.setupModal();
        }
    }

    setupModal() {
        this.overlay = document.getElementById('modal-overlay');
        this.content = document.getElementById('modal-content');
        
        if (!this.overlay || !this.content) {
            console.error('Modal system: Required elements not found');
            return;
        }

        // Setup event listeners
        this.overlay.addEventListener('click', (e) => {
            if (e.target === this.overlay) {
                this.close();
            }
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (this.isOpen()) {
                if (e.key === 'Escape') {
                    this.close();
                } else if (e.key === 'Tab') {
                    this.handleTabKey(e);
                }
            }
        });
    }

    isOpen() {
        return this.overlay && !this.overlay.classList.contains('hidden');
    }

    open(type, options = {}) {
        if (!this.overlay || !this.content) return;

        // Store currently focused element
        this.focusedElementBeforeModal = document.activeElement;

        // Get template and populate content
        const template = document.getElementById(`${type}-modal-template`);
        if (!template) {
            console.error(`Modal template not found: ${type}-modal-template`);
            return;
        }

        const clone = template.content.cloneNode(true);
        this.populateModal(clone, type, options);
        
        // Clear and set content
        this.content.innerHTML = '';
        this.content.appendChild(clone);

        // Show modal
        this.overlay.classList.remove('hidden');
        this.currentModal = type;

        // Focus management
        setTimeout(() => {
            const firstFocusable = this.getFirstFocusableElement();
            if (firstFocusable) {
                firstFocusable.focus();
            }
        }, 100);

        // Prevent body scroll
        document.body.style.overflow = 'hidden';
    }

    close() {
        if (!this.overlay) return;

        this.overlay.classList.add('hidden');
        this.currentModal = null;

        // Restore body scroll
        document.body.style.overflow = '';

        // Restore focus
        if (this.focusedElementBeforeModal) {
            this.focusedElementBeforeModal.focus();
            this.focusedElementBeforeModal = null;
        }

        // Clear content after animation
        setTimeout(() => {
            if (this.content) {
                this.content.innerHTML = '';
            }
        }, 300);
    }

    populateModal(clone, type, options) {
        switch (type) {
            case 'success':
                this.populateSuccessModal(clone, options);
                break;
            case 'error':
                this.populateErrorModal(clone, options);
                break;
            case 'confirmation':
                this.populateConfirmationModal(clone, options);
                break;
            case 'loading':
                this.populateLoadingModal(clone, options);
                break;
            case 'form':
                this.populateFormModal(clone, options);
                break;
            case 'info':
                this.populateInfoModal(clone, options);
                break;
        }
    }

    populateSuccessModal(clone, options) {
        const title = clone.getElementById('success-title');
        const message = clone.getElementById('success-message');
        
        if (title && options.title) title.textContent = options.title;
        if (message && options.message) message.textContent = options.message;

        // Add callback for continue button
        const continueBtn = clone.querySelector('button[onclick="closeModal()"]');
        if (continueBtn && options.onContinue) {
            continueBtn.onclick = () => {
                this.close();
                options.onContinue();
            };
        }
    }

    populateErrorModal(clone, options) {
        const title = clone.getElementById('error-title');
        const message = clone.getElementById('error-message');
        
        if (title && options.title) title.textContent = options.title;
        if (message && options.message) message.textContent = options.message;
    }

    populateConfirmationModal(clone, options) {
        const title = clone.getElementById('confirmation-title');
        const message = clone.getElementById('confirmation-message');
        const actionBtn = clone.getElementById('confirmation-action');
        
        if (title && options.title) title.textContent = options.title;
        if (message && options.message) message.textContent = options.message;
        if (actionBtn && options.confirmText) actionBtn.textContent = options.confirmText;

        if (actionBtn && options.onConfirm) {
            actionBtn.onclick = () => {
                this.close();
                options.onConfirm();
            };
        }
    }

    populateLoadingModal(clone, options) {
        const title = clone.getElementById('loading-title');
        const message = clone.getElementById('loading-message');
        
        if (title && options.title) title.textContent = options.title;
        if (message && options.message) message.textContent = options.message;
    }

    populateFormModal(clone, options) {
        const title = clone.getElementById('form-title');
        const content = clone.getElementById('form-content');
        const submitBtn = clone.getElementById('form-submit');
        
        if (title && options.title) title.textContent = options.title;
        if (content && options.content) content.innerHTML = options.content;
        if (submitBtn && options.submitText) submitBtn.textContent = options.submitText;

        if (submitBtn && options.onSubmit) {
            submitBtn.onclick = (e) => {
                e.preventDefault();
                options.onSubmit(this.content);
            };
        }
    }

    populateInfoModal(clone, options) {
        const title = clone.getElementById('info-title');
        const content = clone.getElementById('info-content');
        
        if (title && options.title) title.textContent = options.title;
        if (content && options.content) content.innerHTML = options.content;
    }

    getFirstFocusableElement() {
        const focusableElements = this.content.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        return focusableElements[0];
    }

    handleTabKey(e) {
        const focusableElements = this.content.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey) {
            if (document.activeElement === firstElement) {
                lastElement.focus();
                e.preventDefault();
            }
        } else {
            if (document.activeElement === lastElement) {
                firstElement.focus();
                e.preventDefault();
            }
        }
    }

    // Convenience methods
    showSuccess(title, message, onContinue = null) {
        this.open('success', { title, message, onContinue });
    }

    showError(title, message) {
        this.open('error', { title, message });
    }

    showConfirmation(title, message, onConfirm, confirmText = 'Confirm') {
        this.open('confirmation', { title, message, onConfirm, confirmText });
    }

    showLoading(title = 'Processing...', message = 'Please wait while we process your request.') {
        this.open('loading', { title, message });
    }

    showForm(title, content, onSubmit, submitText = 'Submit') {
        this.open('form', { title, content, onSubmit, submitText });
    }

    showInfo(title, content) {
        this.open('info', { title, content });
    }
}

// Global modal instance
let modalSystem = null;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    modalSystem = new ModalSystem();
});

// Global functions for backward compatibility
function closeModal() {
    if (modalSystem) {
        modalSystem.close();
    }
}

function showSuccessModal(title, message, onContinue = null) {
    if (modalSystem) {
        modalSystem.showSuccess(title, message, onContinue);
    }
}

function showErrorModal(title, message) {
    if (modalSystem) {
        modalSystem.showError(title, message);
    }
}

function showConfirmationModal(title, message, onConfirm, confirmText = 'Confirm') {
    if (modalSystem) {
        modalSystem.showConfirmation(title, message, onConfirm, confirmText);
    }
}

function showLoadingModal(title, message) {
    if (modalSystem) {
        modalSystem.showLoading(title, message);
    }
}

function showFormModal(title, content, onSubmit, submitText = 'Submit') {
    if (modalSystem) {
        modalSystem.showForm(title, content, onSubmit, submitText);
    }
}

function showInfoModal(title, content) {
    if (modalSystem) {
        modalSystem.showInfo(title, content);
    }
}
