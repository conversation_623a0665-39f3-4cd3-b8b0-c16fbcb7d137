# Dashboard Components Documentation

This directory contains reusable components for the JHCSC Unified Student Services dashboard templates. These components ensure consistency across all dashboards while maintaining the emerald and gold theme.

## Components Overview

### 1. Theme Configuration (`dashboard_theme_config`)

Provides the Tailwind CSS configuration for the emerald and gold color scheme, animations, and custom styles.

**Usage:**
```html
{% extends 'base.html' %}
{% include 'components/dashboard_components.html' %}

{% block extra_head %}
{% include 'components/dashboard_components.html' with block_name='dashboard_theme_config' %}
{% endblock %}
```

### 2. Welcome Section (`welcome_section`)

A hero section with gradient background for dashboard headers.

**Usage:**
```html
{% include 'components/dashboard_components.html' with block_name='welcome_section' title="Welcome back, <PERSON>!" subtitle="Student Dashboard" description="Manage your appointments and services" %}
```

**Parameters:**
- `title`: Main heading text
- `subtitle`: Secondary text (e.g., role or dashboard type)
- `description`: Brief description text
- Custom icon can be provided by overriding the `welcome_icon` block

### 3. Stats Card (`stats_card`)

Displays statistics with icons and gradient backgrounds.

**Usage:**
```html
{% include 'components/dashboard_components.html' with block_name='stats_card' label="Pending" value="12" description="Awaiting processing" icon_color="gold" %}
```

**Parameters:**
- `label`: The statistic label
- `value`: The numeric value
- `description`: Additional context
- `icon_color`: Color theme (emerald, gold, blue, purple, etc.)
- Custom icon can be provided by overriding the `stats_icon` block

### 4. Action Card (`action_card`)

Interactive cards for quick actions and navigation.

**Usage:**
```html
{% include 'components/dashboard_components.html' with block_name='action_card' title="Manage Appointments" description="Review and process student requests" url="/appointments/" button_text="Get Started" bg_color="emerald" %}
```

**Parameters:**
- `title`: Card title
- `description`: Card description
- `url`: Link destination
- `button_text`: Button label
- `bg_color`: Color theme
- Custom icons can be provided by overriding `action_icon` and `action_button_icon` blocks

### 5. Status Badge (`status_badge`)

Consistent status indicators with gradient backgrounds.

**Usage:**
```html
{% include 'components/dashboard_components.html' with block_name='status_badge' status="pending" %}
```

**Supported statuses:**
- `pending`: Gold gradient
- `in_progress`: Blue gradient
- `ready`: Emerald gradient
- `claimed`/`completed`: Gray gradient
- Custom statuses: Purple gradient

### 6. Data Table (`data_table`)

Styled table with emerald header gradient.

**Usage:**
```html
{% include 'components/dashboard_components.html' with block_name='data_table' headers="Student,Service,Status,Date" %}
{% block table_rows %}
    <tr class="hover:bg-gray-50 transition-colors duration-200">
        <td class="px-6 py-4 text-sm font-semibold text-gray-900">John Doe</td>
        <td class="px-6 py-4 text-sm text-gray-600">Transcript Request</td>
        <td class="px-6 py-4">
            {% include 'components/dashboard_components.html' with block_name='status_badge' status="pending" %}
        </td>
        <td class="px-6 py-4 text-sm text-gray-500">Dec 15, 2024</td>
    </tr>
{% endblock %}
```

### 7. Empty State (`empty_state`)

Displays when no data is available.

**Usage:**
```html
{% include 'components/dashboard_components.html' with block_name='empty_state' title="No Appointments" description="No recent appointments found." action_url="/appointments/new/" action_text="Create Appointment" %}
```

**Parameters:**
- `title`: Empty state title
- `description`: Explanation text
- `action_url`: Optional action link
- `action_text`: Action button text
- Custom icon can be provided by overriding the `empty_icon` block

## Color Scheme

### Primary Colors
- **Emerald**: Main brand color for primary actions and highlights
- **Gold**: Accent color for pending states and secondary elements

### Usage Guidelines
- Use emerald for primary navigation, main actions, and success states
- Use gold for pending states, warnings, and accent elements
- Use blue for in-progress states
- Use purple for special statuses
- Use gray for completed/inactive states

## Animation Classes

### Available Animations
- `animate-fade-in`: Smooth fade-in effect (0.6s)
- `animate-slide-up`: Slide up with fade-in (0.6s)
- `card-hover`: Hover effect for cards (0.3s)

### CSS Classes
- `.hero-gradient`: Emerald gradient background for hero sections
- `.text-gradient`: Gold gradient text effect
- `.card-hover`: Hover animation for interactive cards

## Best Practices

1. **Consistency**: Always use these components instead of creating custom styles
2. **Accessibility**: Components include proper ARIA labels and keyboard navigation
3. **Responsiveness**: All components are mobile-first and responsive
4. **Performance**: Components use CSS transitions for smooth animations
5. **Maintainability**: Centralized styling makes updates easier

## Integration Example

Here's how to integrate these components into a new dashboard:

```html
{% extends 'base.html' %}
{% include 'components/dashboard_components.html' %}

{% block extra_head %}
{% include 'components/dashboard_components.html' with block_name='dashboard_theme_config' %}
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-fade-in">
    <!-- Welcome Section -->
    {% include 'components/dashboard_components.html' with block_name='welcome_section' title="Welcome back!" subtitle="Dashboard" description="Your personalized dashboard" %}
    
    <!-- Stats Grid -->
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8 animate-slide-up">
        {% include 'components/dashboard_components.html' with block_name='stats_card' label="Total" value="42" description="Items processed" icon_color="emerald" %}
        <!-- More stats cards... -->
    </div>
    
    <!-- Action Cards -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {% include 'components/dashboard_components.html' with block_name='action_card' title="Quick Action" description="Perform common tasks" url="/action/" button_text="Get Started" bg_color="emerald" %}
        <!-- More action cards... -->
    </div>
</div>
{% endblock %}
```

This approach ensures all dashboards maintain visual consistency while being easy to maintain and update.
