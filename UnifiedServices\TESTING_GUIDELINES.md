# Testing Guidelines for Unified Student Services

## Quick Start

### Running Tests

1. **Run All Tests**
   ```bash
   python run_tests.py
   ```

2. **Run Model Tests (Recommended - All Passing)**
   ```bash
   python manage.py test appointments.tests.test_models -v 2
   ```

3. **Run Specific Test**
   ```bash
   python manage.py test appointments.tests.test_models.AppointmentModelTest.test_qr_code_generation -v 2
   ```

## Test Categories

### ✅ Model Tests (22/22 PASSING)
- **Purpose**: Validate all data models and business logic
- **Status**: All tests passing
- **Coverage**: Complete model functionality
- **Command**: `python manage.py test appointments.tests.test_models`

### ⚠️ View Tests (2/24 PASSING)
- **Purpose**: Test web interface and user interactions
- **Status**: Limited due to audit logging constraints
- **Issues**: Mock request objects incompatible with audit system
- **Command**: `python manage.py test appointments.tests.test_views`

### ⚠️ Security Tests (5/19 PASSING)
- **Purpose**: Validate security features and protections
- **Status**: Core security logic works, integration issues
- **Issues**: Audit logging during security event creation
- **Command**: `python manage.py test appointments.tests.test_security`

### ⚠️ Utility Tests (11/20 PASSING)
- **Purpose**: Test helper functions and management commands
- **Status**: Most utilities work, some edge cases fail
- **Issues**: IP extraction and data isolation
- **Command**: `python manage.py test appointments.tests.test_utils`

## Test Development Guidelines

### Writing New Tests

1. **Follow Django Test Conventions**
   ```python
   from django.test import TestCase
   
   class MyModelTest(TestCase):
       def setUp(self):
           # Create test data
           pass
       
       def test_specific_functionality(self):
           # Test one specific thing
           self.assertEqual(expected, actual)
   ```

2. **Use Descriptive Test Names**
   - `test_appointment_creation` ✅
   - `test_stuff` ❌

3. **Test One Thing Per Test Method**
   - Each test should validate one specific behavior
   - Use multiple test methods for different scenarios

### Test Data Management

1. **Create Minimal Test Data**
   ```python
   def setUp(self):
       self.department = Department.objects.create(
           name='Test Department',
           code='TEST',
           description='Test description'
       )
   ```

2. **Use Unique Identifiers**
   - Avoid hardcoded values that might conflict
   - Use timestamps or UUIDs for unique data

3. **Clean Up After Tests**
   - Django handles database cleanup automatically
   - Manual cleanup only needed for files or external resources

### Mock Objects

1. **Create Complete Mock Requests**
   ```python
   class MockRequest:
       META = {'REMOTE_ADDR': '127.0.0.1', 'HTTP_USER_AGENT': 'Test'}
       method = 'GET'
       path = '/test/'
       
       class MockSession:
           session_key = 'test_session'
       
       session = MockSession()
   ```

2. **Mock External Dependencies**
   ```python
   @patch('appointments.utils.some_external_service')
   def test_with_mock(self, mock_service):
       mock_service.return_value = 'expected_result'
       # Test code here
   ```

## Common Test Patterns

### Testing Model Creation
```python
def test_model_creation(self):
    obj = MyModel.objects.create(field1='value1')
    self.assertEqual(obj.field1, 'value1')
    self.assertIsNotNone(obj.created_at)
```

### Testing Model Methods
```python
def test_model_method(self):
    obj = MyModel.objects.create(field1='value1')
    result = obj.my_method()
    self.assertEqual(result, 'expected_result')
```

### Testing Views
```python
def test_view_response(self):
    self.client.login(username='testuser', password='testpass')
    response = self.client.get(reverse('view_name'))
    self.assertEqual(response.status_code, 200)
```

### Testing Forms
```python
def test_form_validation(self):
    form_data = {'field1': 'value1', 'field2': 'value2'}
    form = MyForm(data=form_data)
    self.assertTrue(form.is_valid())
```

## Debugging Test Failures

### Common Issues

1. **Database Constraint Violations**
   - Check for unique field conflicts
   - Ensure required fields are provided
   - Verify foreign key relationships

2. **Import Errors**
   - Check Python path
   - Verify all dependencies are installed
   - Check for circular imports

3. **Mock Object Issues**
   - Ensure mock objects have all required attributes
   - Check method signatures match expectations
   - Verify return values are correct types

### Debugging Commands

1. **Run Single Test with Verbose Output**
   ```bash
   python manage.py test appointments.tests.test_models.AppointmentModelTest.test_qr_code_generation -v 3
   ```

2. **Use Python Debugger**
   ```python
   import pdb; pdb.set_trace()
   ```

3. **Print Debug Information**
   ```python
   print(f"Debug: {variable_name}")
   ```

## Performance Testing

### Database Performance
```python
from django.test.utils import override_settings
from django.db import connection

def test_query_performance(self):
    with self.assertNumQueries(1):
        list(MyModel.objects.all())
```

### Memory Usage
```python
import tracemalloc

def test_memory_usage(self):
    tracemalloc.start()
    # Test code here
    current, peak = tracemalloc.get_traced_memory()
    tracemalloc.stop()
    self.assertLess(peak, 1024 * 1024)  # Less than 1MB
```

## Continuous Integration

### Test Automation
- Run model tests on every commit (they're stable)
- Run full test suite on pull requests
- Generate coverage reports automatically

### Quality Gates
- Require all model tests to pass
- Maintain minimum coverage threshold
- Check for test performance regressions

## Best Practices

1. **Test Early and Often**
   - Write tests as you develop features
   - Run tests before committing code
   - Fix failing tests immediately

2. **Keep Tests Simple**
   - One assertion per test when possible
   - Clear setup and teardown
   - Minimal test data

3. **Document Complex Tests**
   - Explain why the test exists
   - Document expected behavior
   - Note any special setup requirements

4. **Maintain Test Quality**
   - Refactor tests when code changes
   - Remove obsolete tests
   - Update tests for new requirements

## Conclusion

The test suite provides a solid foundation for quality assurance. Focus on the model tests (which are fully functional) for core validation, and gradually improve the view and integration tests as the audit logging issues are resolved.
