{% extends 'base.html' %}

{% block title %}Staff Dashboard - JHCSC Unified Student Services{% endblock %}

{% block page_title %}Staff Dashboard{% endblock %}

{% block extra_head %}
<!-- Custom Tailwind Config for Emerald & Gold Theme -->
<script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    emerald: {
                        50: '#ecfdf5',
                        100: '#d1fae5',
                        200: '#a7f3d0',
                        300: '#6ee7b7',
                        400: '#34d399',
                        500: '#10b981',
                        600: '#059669',
                        700: '#047857',
                        800: '#065f46',
                        900: '#064e3b',
                        950: '#022c22'
                    },
                    gold: {
                        50: '#fffbeb',
                        100: '#fef3c7',
                        200: '#fde68a',
                        300: '#fcd34d',
                        400: '#fbbf24',
                        500: '#f59e0b',
                        600: '#d97706',
                        700: '#b45309',
                        800: '#92400e',
                        900: '#78350f'
                    }
                },
                animation: {
                    'fade-in': 'fadeIn 0.6s ease-out',
                    'slide-up': 'slideUp 0.6s ease-out',
                    'card-hover': 'cardHover 0.3s ease',
                },
                keyframes: {
                    fadeIn: {
                        '0%': { opacity: '0' },
                        '100%': { opacity: '1' }
                    },
                    slideUp: {
                        '0%': { opacity: '0', transform: 'translateY(20px)' },
                        '100%': { opacity: '1', transform: 'translateY(0)' }
                    },
                    cardHover: {
                        '0%': { transform: 'translateY(0)' },
                        '100%': { transform: 'translateY(-4px)' }
                    }
                }
            }
        }
    }
</script>

<style>
    [x-cloak] { display: none !important; }

    .card-hover {
        transition: all 0.3s ease;
    }

    .card-hover:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .text-gradient {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .hero-gradient {
        background: linear-gradient(135deg, #064e3b 0%, #047857 50%, #059669 100%);
    }
</style>
{% endblock %}

{% block mobile_nav %}
{% include 'navigation/staff_nav.html' %}
{% endblock %}

{% block desktop_nav %}
{% include 'navigation/staff_nav.html' %}
{% endblock %}

{% block content %}
<div class="animate-fade-in">
    <!-- Welcome Section -->
    <div class="hero-gradient rounded-2xl p-8 mb-8 text-white shadow-2xl relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <svg class="w-full h-full" viewBox="0 0 100 100" fill="none">
                <defs>
                    <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                        <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" stroke-width="0.5"/>
                    </pattern>
                </defs>
                <rect width="100" height="100" fill="url(#grid)" />
            </svg>
        </div>

        <div class="relative flex items-center justify-between">
            <div class="flex-1">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold mb-1">Welcome back, {{ user.get_full_name|default:user.username }}!</h1>
                        <p class="text-emerald-100 text-lg font-medium">{{ user.userprofile.department.name }} Staff Portal</p>
                    </div>
                </div>
                <p class="text-emerald-200 text-sm">Manage appointments and assist students efficiently</p>
                <div class="mt-4 flex items-center text-emerald-200 text-sm">
                    <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Last login: {{ user.last_login|date:"M d, Y g:i A"|default:"First time login" }}
                </div>
            </div>
            <div class="hidden lg:block">
                <div class="w-24 h-24 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                    <svg class="w-12 h-12 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Overview -->
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8 animate-slide-up">
        <!-- Pending Appointments -->
        <div class="bg-white overflow-hidden shadow-xl rounded-2xl border border-gray-100 card-hover group">
            <div class="p-6 relative">
                <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-gold-50 to-gold-100 rounded-bl-3xl opacity-50"></div>
                <div class="relative flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-14 h-14 bg-gradient-to-br from-gold-400 to-gold-500 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200">
                            <svg class="h-7 w-7 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-semibold text-gray-500 truncate uppercase tracking-wide">Pending</dt>
                            <dd class="text-3xl font-bold text-gray-900 mb-1">{{ pending_count }}</dd>
                            <dd class="text-xs text-gray-400">Awaiting processing</dd>
                        </dl>
                    </div>
                </div>
                {% if pending_count > 0 %}
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <a href="{% url 'staff_appointment_list' %}?status=pending" class="text-gold-600 hover:text-gold-700 text-sm font-medium transition-colors duration-200">
                        View pending →
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- In Progress -->
        <div class="bg-white overflow-hidden shadow-xl rounded-2xl border border-gray-100 card-hover group">
            <div class="p-6 relative">
                <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-50 to-blue-100 rounded-bl-3xl opacity-50"></div>
                <div class="relative flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-14 h-14 bg-gradient-to-br from-blue-400 to-blue-500 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200">
                            <svg class="h-7 w-7 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-semibold text-gray-500 truncate uppercase tracking-wide">In Progress</dt>
                            <dd class="text-3xl font-bold text-gray-900 mb-1">{{ in_progress_count }}</dd>
                            <dd class="text-xs text-gray-400">Currently processing</dd>
                        </dl>
                    </div>
                </div>
                {% if in_progress_count > 0 %}
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <a href="{% url 'staff_appointment_list' %}?status=in_progress" class="text-blue-600 hover:text-blue-700 text-sm font-medium transition-colors duration-200">
                        View in progress →
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Ready for Pickup -->
        <div class="bg-white overflow-hidden shadow-xl rounded-2xl border border-gray-100 card-hover group">
            <div class="p-6 relative">
                <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-bl-3xl opacity-50"></div>
                <div class="relative flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-14 h-14 bg-gradient-to-br from-emerald-400 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200">
                            <svg class="h-7 w-7 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-semibold text-gray-500 truncate uppercase tracking-wide">Ready</dt>
                            <dd class="text-3xl font-bold text-gray-900 mb-1">{{ ready_count }}</dd>
                            <dd class="text-xs text-gray-400">Ready for pickup</dd>
                        </dl>
                    </div>
                </div>
                {% if ready_count > 0 %}
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <a href="{% url 'staff_appointment_list' %}?status=ready" class="text-emerald-600 hover:text-emerald-700 text-sm font-medium transition-colors duration-200">
                        View ready →
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Completed -->
        <div class="bg-white overflow-hidden shadow-xl rounded-2xl border border-gray-100 card-hover group">
            <div class="p-6 relative">
                <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-purple-50 to-purple-100 rounded-bl-3xl opacity-50"></div>
                <div class="relative flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-14 h-14 bg-gradient-to-br from-purple-400 to-purple-500 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200">
                            <svg class="h-7 w-7 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-semibold text-gray-500 truncate uppercase tracking-wide">Completed</dt>
                            <dd class="text-3xl font-bold text-gray-900 mb-1">{{ completed_count }}</dd>
                            <dd class="text-xs text-gray-400">Successfully processed</dd>
                        </dl>
                    </div>
                </div>
                {% if completed_count > 0 %}
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <a href="{% url 'staff_appointment_list' %}?status=claimed" class="text-purple-600 hover:text-purple-700 text-sm font-medium transition-colors duration-200">
                        View completed →
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white shadow-xl rounded-2xl mb-8 border border-gray-100">
        <div class="px-6 py-8">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-gray-900">Quick Actions</h3>
                <div class="w-8 h-8 bg-gradient-to-r from-emerald-500 to-gold-500 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                </div>
            </div>
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                <a href="{% url 'staff_appointment_list' %}" class="group relative block w-full rounded-xl border-2 border-dashed border-emerald-200 p-6 text-center hover:border-emerald-400 hover:bg-emerald-50 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 transition-all duration-200 card-hover">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />
                        </svg>
                    </div>
                    <span class="block text-lg font-semibold text-gray-900 mb-2">Manage Appointments</span>
                    <span class="block text-sm text-gray-500">Review and manage student service requests</span>
                </a>
                <a href="{% url 'qr_scanner' %}" class="group relative block w-full rounded-xl border-2 border-dashed border-gold-200 p-6 text-center hover:border-gold-400 hover:bg-gold-50 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2 transition-all duration-200 card-hover">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-gold-500 to-gold-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 013.75 9.375v-4.5zM3.75 14.625c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5a1.125 1.125 0 01-1.125-1.125v-4.5zM13.5 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 0113.5 9.375v-4.5z" />
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 6.75h.75v.75h-.75v-.75zM6.75 16.5h.75v.75h-.75v-.75zM16.5 6.75h.75v.75h-.75v-.75zM13.5 13.5h4.5v4.5h-4.5v-4.5z" />
                        </svg>
                    </div>
                    <span class="block text-lg font-semibold text-gray-900 mb-2">QR Code Scanner</span>
                    <span class="block text-sm text-gray-500">Scan student QR codes to validate and release documents</span>
                </a>
                <a href="#" class="group relative block w-full rounded-xl border-2 border-dashed border-blue-200 p-6 text-center hover:border-blue-400 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 card-hover">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
                        </svg>
                    </div>
                    <span class="block text-lg font-semibold text-gray-900 mb-2">Department Reports</span>
                    <span class="block text-sm text-gray-500">View analytics and performance metrics</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Recent Appointments -->
    <div class="bg-white shadow-xl rounded-2xl border border-gray-100">
        <div class="px-6 py-8">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900">Recent Appointments</h3>
                </div>
                <a href="{% url 'staff_appointment_list' %}" class="inline-flex items-center text-emerald-600 hover:text-emerald-700 font-medium text-sm transition-colors duration-200">
                    View all
                    <svg class="ml-1 w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </a>
            </div>
            {% if recent_appointments %}
            <div class="space-y-3">
                {% for appointment in recent_appointments|slice:":5" %}
                <div class="bg-gray-50 rounded-xl p-4 hover:bg-gray-100 transition-all duration-200 card-hover group border border-gray-100 hover:border-emerald-200">
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <div class="h-12 w-12 rounded-xl bg-gradient-to-br from-emerald-500 to-emerald-600 flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-200">
                                <span class="text-sm font-bold text-white">{{ appointment.service.department.code|default:"SVC" }}</span>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between mb-1">
                                <p class="text-lg font-semibold text-gray-900 truncate">{{ appointment.service.name }}</p>
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold
                                    {% if appointment.status == 'pending' %}bg-gradient-to-r from-gold-100 to-gold-200 text-gold-800
                                    {% elif appointment.status == 'in_progress' %}bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800
                                    {% elif appointment.status == 'ready' %}bg-gradient-to-r from-emerald-100 to-emerald-200 text-emerald-800
                                    {% elif appointment.status == 'claimed' %}bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800
                                    {% else %}bg-gradient-to-r from-red-100 to-red-200 text-red-800{% endif %}">
                                    {{ appointment.get_status_display }}
                                </span>
                            </div>
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-600 font-medium">{{ appointment.student.get_full_name }}</p>
                                    <p class="text-xs text-gray-400">{{ appointment.created_at|date:"M d, Y g:i A" }}</p>
                                </div>
                                <a href="{% url 'staff_appointment_detail' appointment.appointment_id %}" class="text-emerald-600 hover:text-emerald-700 text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                    View details →
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% if recent_appointments|length > 5 %}
            <div class="mt-6 pt-4 border-t border-gray-100 text-center">
                <a href="{% url 'staff_appointment_list' %}" class="inline-flex items-center px-4 py-2 border border-emerald-300 text-sm font-medium rounded-lg text-emerald-700 bg-emerald-50 hover:bg-emerald-100 transition-colors duration-200">
                    <svg class="mr-2 w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    View All {{ appointments.count }} Appointments
                </a>
            </div>
            {% endif %}
            {% else %}
            <div class="text-center py-12">
                <div class="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-emerald-100 to-emerald-200 rounded-2xl flex items-center justify-center">
                    <svg class="h-10 w-10 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">No Recent Appointments</h3>
                <p class="text-gray-500 mb-6 max-w-sm mx-auto">No recent appointments found for your department. New appointments will appear here once students start booking services.</p>
                <a href="{% url 'staff_appointment_list' %}" class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 transition-all duration-200 shadow-lg">
                    <svg class="mr-2 w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    View All Appointments
                </a>
            </div>
            {% endif %}
        </div>
    </div>

</div>
{% endblock %}
