"""
Test cases for utility functions and helpers
"""

from django.test import TestCase
from django.contrib.auth.models import User
from django.core.management import call_command
from django.utils import timezone
from datetime import timedelta
import json
import tempfile
import os

from appointments.models import (
    UserProfile, Department, Service, ServiceRequirement,
    Appointment, SecurityEvent, AuditLog
)
from appointments.management.commands.setup_sample_data import Command as SetupCommand
from appointments.management.commands.generate_security_events import Command as SecurityCommand


class ManagementCommandTest(TestCase):
    """Test management commands"""
    
    def test_setup_sample_data_command(self):
        """Test setup_sample_data management command"""
        # Run the command
        call_command('setup_sample_data')
        
        # Verify data was created
        self.assertTrue(Department.objects.exists())
        self.assertTrue(Service.objects.exists())
        self.assertTrue(ServiceRequirement.objects.exists())
        self.assertTrue(User.objects.filter(userprofile__role='admin').exists())
        self.assertTrue(User.objects.filter(userprofile__role='office_staff').exists())
        self.assertTrue(User.objects.filter(userprofile__role='student').exists())
    
    def test_generate_security_events_command(self):
        """Test generate_security_events management command"""
        # Create a user first
        User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        initial_count = SecurityEvent.objects.count()
        
        # Run the command
        call_command('generate_security_events', '--count', '10')
        
        # Verify security events were created
        final_count = SecurityEvent.objects.count()
        self.assertEqual(final_count, initial_count + 10)
    
    def test_command_with_options(self):
        """Test management command with various options"""
        # Test with different count
        call_command('generate_security_events', '--count', '5')
        
        # Verify correct number of events
        recent_events = SecurityEvent.objects.filter(
            timestamp__gte=timezone.now() - timedelta(minutes=1)
        ).count()
        self.assertGreaterEqual(recent_events, 5)


class ModelUtilityTest(TestCase):
    """Test model utility methods"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        self.department = Department.objects.create(
            name='Test Department',
            description='Test description'
        )
        
        self.service = Service.objects.create(
            name='Test Service',
            department=self.department
        )
    
    def test_appointment_id_uniqueness(self):
        """Test appointment ID generation uniqueness"""
        appointment_ids = set()
        
        # Create multiple appointments
        for i in range(100):
            appointment = Appointment.objects.create(
                student=self.user,
                service=self.service
            )
            appointment_ids.add(appointment.appointment_id)
        
        # Verify all IDs are unique
        self.assertEqual(len(appointment_ids), 100)
    
    def test_department_slug_generation(self):
        """Test department slug generation"""
        test_cases = [
            ('Simple Name', 'simple-name'),
            ('Name With Spaces', 'name-with-spaces'),
            ('Name & Special Characters!', 'name-special-characters'),
            ('UPPERCASE name', 'uppercase-name'),
        ]
        
        for name, expected_slug in test_cases:
            dept = Department.objects.create(
                name=name,
                description='Test'
            )
            self.assertEqual(dept.slug, expected_slug)
    
    def test_user_profile_role_display(self):
        """Test user profile role display"""
        test_roles = [
            ('student', 'Student'),
            ('office_staff', 'Office Staff'),
            ('admin', 'Administrator'),
        ]
        
        for role_code, role_display in test_roles:
            self.user.userprofile.role = role_code
            self.user.userprofile.save()
            
            self.assertEqual(
                self.user.userprofile.get_role_display(),
                role_display
            )
    
    def test_appointment_status_transitions(self):
        """Test valid appointment status transitions"""
        appointment = Appointment.objects.create(
            student=self.user,
            service=self.service
        )
        
        # Test valid transitions
        valid_transitions = [
            ('pending', 'under_review'),
            ('under_review', 'approved'),
            ('approved', 'ready'),
            ('ready', 'completed'),
        ]
        
        for from_status, to_status in valid_transitions:
            appointment.status = from_status
            appointment.save()
            
            appointment.status = to_status
            appointment.save()  # Should not raise exception
            
            self.assertEqual(appointment.status, to_status)


class QRCodeUtilityTest(TestCase):
    """Test QR code utility functions"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        self.department = Department.objects.create(
            name='Test Department',
            description='Test'
        )
        
        self.service = Service.objects.create(
            name='Test Service',
            department=self.department
        )
        
        self.appointment = Appointment.objects.create(
            student=self.user,
            service=self.service
        )
    
    def test_qr_code_data_structure(self):
        """Test QR code data structure"""
        qr_data, qr_image = self.appointment.generate_qr_code()
        
        # Verify required fields
        required_fields = [
            'appointment_id',
            'student_id',
            'service',
            'department',
            'timestamp',
            'hash'
        ]
        
        for field in required_fields:
            self.assertIn(field, qr_data)
    
    def test_qr_code_hash_consistency(self):
        """Test QR code hash consistency"""
        qr_data1, _ = self.appointment.generate_qr_code()
        qr_data2, _ = self.appointment.generate_qr_code()
        
        # Hash should be consistent for same data
        self.assertEqual(qr_data1['hash'], qr_data2['hash'])
    
    def test_qr_code_image_format(self):
        """Test QR code image format"""
        _, qr_image = self.appointment.generate_qr_code()
        
        # Should be base64 encoded PNG
        self.assertTrue(qr_image.startswith('data:image/png;base64,'))
        
        # Should be valid base64
        import base64
        try:
            base64_data = qr_image.split(',')[1]
            base64.b64decode(base64_data)
        except Exception:
            self.fail("QR code image is not valid base64")
    
    def test_qr_code_validation_edge_cases(self):
        """Test QR code validation edge cases"""
        # Test with invalid JSON
        is_valid, message = self.appointment.validate_qr_code("invalid json")
        self.assertFalse(is_valid)
        self.assertIn('invalid', message.lower())
        
        # Test with missing fields
        incomplete_data = {
            'appointment_id': self.appointment.appointment_id,
            # Missing other required fields
        }
        
        is_valid, message = self.appointment.validate_qr_code(
            json.dumps(incomplete_data)
        )
        self.assertFalse(is_valid)
        
        # Test with wrong appointment ID
        wrong_data = {
            'appointment_id': 'WRONG-ID',
            'student_id': self.user.id,
            'service': self.service.name,
            'hash': 'invalid'
        }
        
        is_valid, message = self.appointment.validate_qr_code(
            json.dumps(wrong_data)
        )
        self.assertFalse(is_valid)


class SecurityUtilityTest(TestCase):
    """Test security utility functions"""
    
    def test_ip_address_extraction(self):
        """Test IP address extraction from request"""
        from appointments.models import AuditLog
        
        # Test with X-Forwarded-For header
        class MockRequest1:
            META = {'HTTP_X_FORWARDED_FOR': '***********, ***********'}
        
        ip = AuditLog.get_client_ip(MockRequest1())
        self.assertEqual(ip, '***********')
        
        # Test with REMOTE_ADDR
        class MockRequest2:
            META = {'REMOTE_ADDR': '*************'}
        
        ip = AuditLog.get_client_ip(MockRequest2())
        self.assertEqual(ip, '*************')
        
        # Test with no IP information
        class MockRequest3:
            META = {}
        
        ip = AuditLog.get_client_ip(MockRequest3())
        self.assertEqual(ip, 'Unknown')
    
    def test_audit_log_data_serialization(self):
        """Test audit log additional data serialization"""
        user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        test_data = {
            'key1': 'value1',
            'key2': 123,
            'key3': ['item1', 'item2']
        }
        
        log = AuditLog.objects.create(
            user=user,
            action='test_action',
            description='Test',
            additional_data=json.dumps(test_data)
        )
        
        # Verify data can be deserialized
        stored_data = json.loads(log.additional_data)
        self.assertEqual(stored_data, test_data)
    
    def test_security_event_severity_validation(self):
        """Test security event severity validation"""
        valid_severities = ['low', 'medium', 'high', 'critical']
        
        for severity in valid_severities:
            event = SecurityEvent.objects.create(
                event_type='test_event',
                severity=severity,
                description='Test event'
            )
            self.assertEqual(event.severity, severity)


class PerformanceTest(TestCase):
    """Test performance-related functionality"""
    
    def setUp(self):
        # Create test data
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        self.department = Department.objects.create(
            name='Test Department',
            description='Test'
        )
        
        self.service = Service.objects.create(
            name='Test Service',
            department=self.department
        )
    
    def test_bulk_appointment_creation(self):
        """Test bulk appointment creation performance"""
        import time
        
        start_time = time.time()
        
        # Create 100 appointments
        appointments = []
        for i in range(100):
            appointments.append(Appointment(
                student=self.user,
                service=self.service,
                purpose=f'Test appointment {i}'
            ))
        
        Appointment.objects.bulk_create(appointments)
        
        end_time = time.time()
        creation_time = end_time - start_time
        
        # Should complete within reasonable time (adjust threshold as needed)
        self.assertLess(creation_time, 5.0)  # 5 seconds
        
        # Verify all appointments were created
        self.assertEqual(
            Appointment.objects.filter(student=self.user).count(),
            100
        )
    
    def test_audit_log_query_performance(self):
        """Test audit log query performance with indexes"""
        # Create test audit logs
        logs = []
        for i in range(1000):
            logs.append(AuditLog(
                user=self.user,
                action='test_action',
                description=f'Test log {i}',
                ip_address='***********'
            ))
        
        AuditLog.objects.bulk_create(logs)
        
        import time
        
        # Test query performance with indexes
        start_time = time.time()
        
        # Query by user and timestamp (should use index)
        recent_logs = AuditLog.objects.filter(
            user=self.user,
            timestamp__gte=timezone.now() - timedelta(hours=1)
        )[:10]
        
        list(recent_logs)  # Force evaluation
        
        end_time = time.time()
        query_time = end_time - start_time
        
        # Should be fast with proper indexing
        self.assertLess(query_time, 1.0)  # 1 second
    
    def test_security_event_aggregation(self):
        """Test security event aggregation performance"""
        # Create test security events
        events = []
        event_types = ['failed_login', 'suspicious_activity', 'unauthorized_access']
        severities = ['low', 'medium', 'high']
        
        for i in range(500):
            events.append(SecurityEvent(
                event_type=event_types[i % len(event_types)],
                severity=severities[i % len(severities)],
                description=f'Test event {i}',
                ip_address='***********'
            ))
        
        SecurityEvent.objects.bulk_create(events)
        
        import time
        
        # Test aggregation query performance
        start_time = time.time()
        
        # Aggregate by event type and severity
        from django.db.models import Count
        aggregation = SecurityEvent.objects.values(
            'event_type', 'severity'
        ).annotate(
            count=Count('id')
        )
        
        list(aggregation)  # Force evaluation
        
        end_time = time.time()
        aggregation_time = end_time - start_time
        
        # Should complete quickly
        self.assertLess(aggregation_time, 2.0)  # 2 seconds


class DataIntegrityTest(TestCase):
    """Test data integrity and consistency"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        self.department = Department.objects.create(
            name='Test Department',
            description='Test'
        )
        
        self.service = Service.objects.create(
            name='Test Service',
            department=self.department
        )
    
    def test_cascade_deletion(self):
        """Test proper cascade deletion"""
        # Create appointment with requirements
        appointment = Appointment.objects.create(
            student=self.user,
            service=self.service
        )
        
        requirement = ServiceRequirement.objects.create(
            service=self.service,
            name='Test Requirement',
            description='Test'
        )
        
        # Delete service
        service_id = self.service.id
        self.service.delete()
        
        # Verify related objects are handled properly
        self.assertFalse(
            Appointment.objects.filter(service_id=service_id).exists()
        )
        self.assertFalse(
            ServiceRequirement.objects.filter(service_id=service_id).exists()
        )
    
    def test_user_profile_consistency(self):
        """Test user profile consistency"""
        # Verify user profile was created automatically
        self.assertTrue(hasattr(self.user, 'userprofile'))
        
        # Test profile updates
        self.user.userprofile.role = 'office_staff'
        self.user.userprofile.department = self.department
        self.user.userprofile.save()
        
        # Verify changes persisted
        self.user.refresh_from_db()
        self.assertEqual(self.user.userprofile.role, 'office_staff')
        self.assertEqual(self.user.userprofile.department, self.department)
    
    def test_appointment_requirement_consistency(self):
        """Test appointment requirement consistency"""
        # Create service with requirements
        req1 = ServiceRequirement.objects.create(
            service=self.service,
            name='Requirement 1',
            description='Test',
            is_required=True
        )
        
        req2 = ServiceRequirement.objects.create(
            service=self.service,
            name='Requirement 2',
            description='Test',
            is_required=False
        )
        
        # Create appointment
        appointment = Appointment.objects.create(
            student=self.user,
            service=self.service
        )
        
        # Verify appointment requirements were created
        app_reqs = appointment.appointmentrequirement_set.all()
        self.assertEqual(app_reqs.count(), 2)
        
        # Verify requirement mapping
        req_names = [ar.requirement.name for ar in app_reqs]
        self.assertIn('Requirement 1', req_names)
        self.assertIn('Requirement 2', req_names)
