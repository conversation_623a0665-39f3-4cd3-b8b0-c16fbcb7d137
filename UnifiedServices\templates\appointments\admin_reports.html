{% extends 'base.html' %}
{% load static %}

{% block title %}System Reports - Admin - JHCSC Unified Services{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">System Reports</h1>
                    <p class="mt-1 text-sm text-gray-500">Analytics and system insights</p>
                </div>
                <a href="{% url 'admin_dashboard' %}" class="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700">
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <nav class="flex space-x-8 mb-8">
            <a href="{% url 'admin_dashboard' %}" class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">
                Dashboard
            </a>
            <a href="{% url 'admin_appointment_list' %}" class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">
                All Appointments
            </a>
            <a href="{% url 'admin_user_list' %}" class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">
                User Management
            </a>
            <a href="{% url 'admin_department_list' %}" class="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">
                Departments
            </a>
            <a href="{% url 'admin_reports' %}" class="bg-indigo-100 text-indigo-700 px-3 py-2 rounded-md text-sm font-medium">
                Reports
            </a>
        </nav>

        <!-- Date Range Filter -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-4 py-5 sm:p-6">
                <form method="get" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700">Start Date</label>
                        <input type="date" name="start_date" id="start_date" value="{{ start_date|date:'Y-m-d' }}"
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700">End Date</label>
                        <input type="date" name="end_date" id="end_date" value="{{ end_date|date:'Y-m-d' }}"
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="w-full bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700">
                            Generate Report
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Summary Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Appointments</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ total_appointments }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Completed</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ completed_appointments }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Completion Rate</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ completion_rate|floatformat:1 }}%</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Department Breakdown -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Department Breakdown</h3>
                    <div class="space-y-4">
                        {% for dept in dept_breakdown %}
                        <div class="border rounded-lg p-4">
                            <div class="flex justify-between items-center mb-2">
                                <h4 class="font-medium text-gray-900">{{ dept.department.name }}</h4>
                                <span class="text-sm text-gray-500">{{ dept.total }} total</span>
                            </div>
                            <div class="grid grid-cols-4 gap-2 text-sm">
                                <div class="text-center">
                                    <div class="text-yellow-600 font-medium">{{ dept.pending }}</div>
                                    <div class="text-gray-500">Pending</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-blue-600 font-medium">{{ dept.in_progress }}</div>
                                    <div class="text-gray-500">In Progress</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-purple-600 font-medium">{{ dept.ready }}</div>
                                    <div class="text-gray-500">Ready</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-green-600 font-medium">{{ dept.claimed }}</div>
                                    <div class="text-gray-500">Claimed</div>
                                </div>
                            </div>
                            {% if dept.total > 0 %}
                            <div class="mt-2">
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    {% widthratio dept.claimed dept.total 100 as completion_width %}
                                    <div class="bg-green-600 h-2 rounded-full" style="width: {{ completion_width }}%"></div>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">{% widthratio dept.claimed dept.total 100 %}% completion rate</p>
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Daily Activity Chart -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Daily Activity</h3>
                    <div class="space-y-2">
                        {% for day in daily_counts %}
                        <div class="flex items-center">
                            <div class="w-20 text-sm text-gray-500">{{ day.date|date:"M d" }}</div>
                            <div class="flex-1 mx-4">
                                <div class="w-full bg-gray-200 rounded-full h-4">
                                    {% if daily_counts and day.count > 0 %}
                                        <div class="bg-indigo-600 h-4 rounded-full" style="width: {% widthratio day.count 10 100 %}%"></div>
                                    {% else %}
                                        <div class="bg-indigo-600 h-4 rounded-full" style="width: 0%"></div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="w-8 text-sm text-gray-900 text-right">{{ day.count }}</div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Export Options -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Export Options</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button class="bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700 disabled:opacity-50" disabled>
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Export to Excel
                    </button>
                    <button class="bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 disabled:opacity-50" disabled>
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                        Export to PDF
                    </button>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50" disabled>
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Export to CSV
                    </button>
                </div>
                <p class="mt-2 text-sm text-gray-500">Export functionality will be available in a future update.</p>
            </div>
        </div>

        <!-- Report Period Summary -->
        <div class="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-6">
            <div class="flex items-center">
                <svg class="h-5 w-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <span class="text-sm text-gray-600">
                    Report period: {{ start_date|date:"F d, Y" }} to {{ end_date|date:"F d, Y" }}
                    ({{ daily_counts|length }} days)
                </span>
            </div>
        </div>
    </div>
</div>

<script>
// Add some basic interactivity for future enhancements
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form when dates change
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');
    
    if (startDate && endDate) {
        startDate.addEventListener('change', function() {
            if (endDate.value && startDate.value <= endDate.value) {
                // Auto-submit could be added here
            }
        });
        
        endDate.addEventListener('change', function() {
            if (startDate.value && endDate.value >= startDate.value) {
                // Auto-submit could be added here
            }
        });
    }
});
</script>
{% endblock %}
