"""
Management command to generate sample security events for testing
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta
import random

from appointments.models import SecurityEvent, AuditLog


class Command(BaseCommand):
    help = 'Generate sample security events for testing the security dashboard'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=50,
            help='Number of security events to generate'
        )

    def handle(self, *args, **options):
        count = options['count']
        
        # Sample data for generating realistic security events
        event_types = [
            'failed_login',
            'multiple_failed_logins',
            'suspicious_activity',
            'unauthorized_access',
            'file_access_violation',
            'qr_tampering',
            'session_hijacking',
            'brute_force',
            'sql_injection',
            'xss_attempt',
        ]
        
        severities = ['low', 'medium', 'high', 'critical']
        severity_weights = [40, 30, 20, 10]  # More low severity events
        
        ip_addresses = [
            '*************',
            '*********',
            '***********',
            '************',
            '************',
            '**********',
            '************',
            '*************',
        ]
        
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)',
            'Mozilla/5.0 (Android 11; Mobile; rv:68.0) Gecko/68.0',
        ]
        
        descriptions = {
            'failed_login': [
                'Failed login attempt with invalid credentials',
                'Login failed for non-existent user account',
                'Password authentication failed',
            ],
            'multiple_failed_logins': [
                'Multiple consecutive failed login attempts detected',
                'Brute force login pattern identified',
                'Repeated authentication failures from same IP',
            ],
            'suspicious_activity': [
                'Unusual access pattern detected',
                'Abnormal user behavior identified',
                'Suspicious file access sequence',
            ],
            'unauthorized_access': [
                'Attempt to access restricted resource',
                'Unauthorized page access attempt',
                'Access denied to protected endpoint',
            ],
            'file_access_violation': [
                'Attempt to upload malicious file',
                'File type validation failed',
                'Suspicious file content detected',
            ],
            'qr_tampering': [
                'QR code integrity check failed',
                'Invalid QR code signature detected',
                'Tampered QR code scan attempt',
            ],
            'session_hijacking': [
                'Potential session hijacking detected',
                'Session token anomaly identified',
                'Suspicious session activity',
            ],
            'brute_force': [
                'Brute force attack pattern detected',
                'Automated attack attempt identified',
                'High frequency request pattern',
            ],
            'sql_injection': [
                'SQL injection attempt in form data',
                'Malicious SQL pattern detected',
                'Database attack attempt blocked',
            ],
            'xss_attempt': [
                'Cross-site scripting attempt detected',
                'Malicious script injection blocked',
                'XSS payload identified in input',
            ],
        }
        
        # Get some users for events
        users = list(User.objects.all()[:5])
        
        created_events = 0
        
        for i in range(count):
            # Generate random timestamp within last 30 days
            days_ago = random.randint(0, 30)
            hours_ago = random.randint(0, 23)
            minutes_ago = random.randint(0, 59)
            
            timestamp = timezone.now() - timedelta(
                days=days_ago,
                hours=hours_ago,
                minutes=minutes_ago
            )
            
            # Select random event type and severity
            event_type = random.choice(event_types)
            severity = random.choices(severities, weights=severity_weights)[0]
            
            # Select random user (or None for anonymous events)
            user = random.choice(users + [None, None])  # Higher chance of anonymous
            
            # Generate description
            description = random.choice(descriptions[event_type])
            
            # Create security event
            event = SecurityEvent.objects.create(
                event_type=event_type,
                severity=severity,
                user=user,
                ip_address=random.choice(ip_addresses),
                user_agent=random.choice(user_agents),
                description=description,
                request_path=f'/api/endpoint/{random.randint(1, 100)}',
                timestamp=timestamp,
                resolved=random.choice([True, False]) if severity in ['low', 'medium'] else False
            )
            
            # If resolved, set resolution details
            if event.resolved:
                event.resolved_by = random.choice(users)
                event.resolved_at = timestamp + timedelta(
                    hours=random.randint(1, 48)
                )
                event.save()
            
            created_events += 1
            
            # Also create corresponding audit log entries for some events
            if random.choice([True, False]):
                AuditLog.objects.create(
                    user=user,
                    action='security_violation',
                    description=f'Security event: {description}',
                    ip_address=event.ip_address,
                    user_agent=event.user_agent,
                    timestamp=timestamp,
                    additional_data=f'{{"event_type": "{event_type}", "severity": "{severity}"}}'
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {created_events} security events'
            )
        )
        
        # Print summary
        total_events = SecurityEvent.objects.count()
        unresolved_critical = SecurityEvent.objects.filter(
            severity='critical', resolved=False
        ).count()
        unresolved_high = SecurityEvent.objects.filter(
            severity='high', resolved=False
        ).count()
        
        self.stdout.write(f'Total security events in database: {total_events}')
        self.stdout.write(f'Unresolved critical events: {unresolved_critical}')
        self.stdout.write(f'Unresolved high severity events: {unresolved_high}')
