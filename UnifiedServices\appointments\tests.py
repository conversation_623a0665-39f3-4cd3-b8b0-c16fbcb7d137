from django.test import TestCase
from django.contrib.auth.models import User
from .models import UserProfile, Department, Service, ServiceRequirement, Appointment, AppointmentRequirement


class AutoRefreshFunctionalityTest(TestCase):
    """Test automatic page refresh and status update functionality"""

    def setUp(self):
        """Set up test data"""
        # Create test department
        self.department = Department.objects.create(
            name="Test Department",
            description="Test department for auto-refresh testing"
        )

        # Create test service with requirements
        self.service = Service.objects.create(
            name="Test Service",
            description="Test service with multiple requirements",
            department=self.department,
            is_active=True
        )

        # Create service requirements
        self.req1 = ServiceRequirement.objects.create(
            service=self.service,
            name="Requirement 1",
            description="First requirement",
            order=1
        )

        self.req2 = ServiceRequirement.objects.create(
            service=self.service,
            name="Requirement 2",
            description="Second requirement",
            order=2
        )

        self.req3 = ServiceRequirement.objects.create(
            service=self.service,
            name="Requirement 3",
            description="Third requirement",
            order=3
        )

        # Create test users
        self.student = User.objects.create_user(
            username='teststudent',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='Student'
        )

        self.staff = User.objects.create_user(
            username='teststaff',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='Staff'
        )

        # Create user profiles
        UserProfile.objects.create(
            user=self.student,
            role='student',
            department=self.department
        )

        UserProfile.objects.create(
            user=self.staff,
            role='office_staff',
            department=self.department
        )

        # Create test appointment
        self.appointment = Appointment.objects.create(
            student=self.student,
            service=self.service,
            status='pending'
        )

    def test_automatic_status_calculation(self):
        """Test that appointment status updates automatically based on requirement completion"""
        # Initially should be pending
        self.assertEqual(self.appointment.status, 'pending')

        # Complete first requirement - should change to in_progress
        req1 = self.appointment.requirements.get(requirement=self.req1)
        req1.is_completed = True
        req1.completed_by = self.staff
        req1.save()

        self.appointment.update_status()
        self.appointment.refresh_from_db()
        self.assertEqual(self.appointment.status, 'in_progress')

        # Complete second requirement - should stay in_progress
        req2 = self.appointment.requirements.get(requirement=self.req2)
        req2.is_completed = True
        req2.completed_by = self.staff
        req2.save()

        self.appointment.update_status()
        self.appointment.refresh_from_db()
        self.assertEqual(self.appointment.status, 'in_progress')

        # Complete third requirement - should change to ready
        req3 = self.appointment.requirements.get(requirement=self.req3)
        req3.is_completed = True
        req3.completed_by = self.staff
        req3.save()

        self.appointment.update_status()
        self.appointment.refresh_from_db()
        self.assertEqual(self.appointment.status, 'ready')

    def test_completion_percentage_calculation(self):
        """Test completion percentage calculation"""
        # Initially 0%
        self.assertEqual(self.appointment.completion_percentage, 0)

        # Complete one requirement - should be 33%
        req1 = self.appointment.requirements.get(requirement=self.req1)
        req1.is_completed = True
        req1.save()

        self.assertEqual(self.appointment.completion_percentage, 33)

        # Complete second requirement - should be 66%
        req2 = self.appointment.requirements.get(requirement=self.req2)
        req2.is_completed = True
        req2.save()

        self.assertEqual(self.appointment.completion_percentage, 66)

        # Complete third requirement - should be 100%
        req3 = self.appointment.requirements.get(requirement=self.req3)
        req3.is_completed = True
        req3.save()

        self.assertEqual(self.appointment.completion_percentage, 100)
