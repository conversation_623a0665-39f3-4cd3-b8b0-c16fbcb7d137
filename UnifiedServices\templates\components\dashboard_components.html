{% comment %}
Shared Dashboard Components for JHCSC Unified Student Services
This file contains reusable components and styles for all dashboard templates
{% endcomment %}

{% comment %} Emerald & Gold Theme Configuration {% endcomment %}
{% block dashboard_theme_config %}
<!-- Custom Tailwind Config for Emerald & Gold Theme -->
<script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    emerald: {
                        50: '#ecfdf5',
                        100: '#d1fae5',
                        200: '#a7f3d0',
                        300: '#6ee7b7',
                        400: '#34d399',
                        500: '#10b981',
                        600: '#059669',
                        700: '#047857',
                        800: '#065f46',
                        900: '#064e3b',
                        950: '#022c22'
                    },
                    gold: {
                        50: '#fffbeb',
                        100: '#fef3c7',
                        200: '#fde68a',
                        300: '#fcd34d',
                        400: '#fbbf24',
                        500: '#f59e0b',
                        600: '#d97706',
                        700: '#b45309',
                        800: '#92400e',
                        900: '#78350f'
                    }
                },
                animation: {
                    'fade-in': 'fadeIn 0.6s ease-out',
                    'slide-up': 'slideUp 0.6s ease-out',
                    'card-hover': 'cardHover 0.3s ease',
                },
                keyframes: {
                    fadeIn: {
                        '0%': { opacity: '0' },
                        '100%': { opacity: '1' }
                    },
                    slideUp: {
                        '0%': { opacity: '0', transform: 'translateY(20px)' },
                        '100%': { opacity: '1', transform: 'translateY(0)' }
                    },
                    cardHover: {
                        '0%': { transform: 'translateY(0)' },
                        '100%': { transform: 'translateY(-4px)' }
                    }
                }
            }
        }
    }
</script>

<style>
    [x-cloak] { display: none !important; }
    
    .card-hover {
        transition: all 0.3s ease;
    }
    
    .card-hover:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    
    .text-gradient {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .hero-gradient {
        background: linear-gradient(135deg, #064e3b 0%, #047857 50%, #059669 100%);
    }
</style>
{% endblock %}

{% comment %} Welcome Section Component {% endcomment %}
{% block welcome_section %}
{% with title=title|default:"Welcome back!" subtitle=subtitle|default:"Dashboard" description=description|default:"Manage your tasks efficiently" %}
<div class="hero-gradient rounded-2xl p-8 mb-8 text-white shadow-2xl">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold mb-2">{{ title }}</h1>
            <p class="text-emerald-100 text-lg">{{ subtitle }}</p>
            <p class="text-emerald-200 text-sm mt-2">{{ description }}</p>
        </div>
        <div class="hidden md:block">
            <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                {% block welcome_icon %}
                <svg class="w-10 h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                {% endblock %}
            </div>
        </div>
    </div>
</div>
{% endwith %}
{% endblock %}

{% comment %} Stats Card Component {% endcomment %}
{% block stats_card %}
{% with icon_color=icon_color|default:"emerald" %}
<div class="bg-white overflow-hidden shadow-xl rounded-2xl border border-gray-100 card-hover">
    <div class="p-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gradient-to-br from-{{ icon_color }}-400 to-{{ icon_color }}-500 rounded-xl flex items-center justify-center shadow-lg">
                    {% block stats_icon %}
                    <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    {% endblock %}
                </div>
            </div>
            <div class="ml-5 w-0 flex-1">
                <dl>
                    <dt class="text-sm font-semibold text-gray-500 truncate">{{ label }}</dt>
                    <dd class="text-3xl font-bold text-gray-900">{{ value }}</dd>
                    <dd class="text-xs text-gray-400 mt-1">{{ description }}</dd>
                </dl>
            </div>
        </div>
    </div>
</div>
{% endwith %}
{% endblock %}

{% comment %} Action Card Component {% endcomment %}
{% block action_card %}
{% with bg_color=bg_color|default:"emerald" %}
<div class="bg-white shadow-xl rounded-2xl border border-gray-100 card-hover">
    <div class="px-6 py-8">
        <div class="flex items-center justify-between mb-4">
            <div class="w-12 h-12 bg-gradient-to-br from-{{ bg_color }}-500 to-{{ bg_color }}-600 rounded-xl flex items-center justify-center shadow-lg">
                {% block action_icon %}
                <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                {% endblock %}
            </div>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-3">{{ title }}</h3>
        <p class="text-gray-600 mb-6">{{ description }}</p>
        <a href="{{ url }}"
           class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-semibold rounded-xl shadow-lg text-white bg-gradient-to-r from-{{ bg_color }}-600 to-{{ bg_color }}-700 hover:from-{{ bg_color }}-700 hover:to-{{ bg_color }}-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-{{ bg_color }}-500 transition-all duration-200">
            {% block action_button_icon %}
            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
            {% endblock %}
            {{ button_text }}
        </a>
    </div>
</div>
{% endwith %}
{% endblock %}

{% comment %} Status Badge Component {% endcomment %}
{% block status_badge %}
{% with status=status|default:"pending" %}
{% if status == 'pending' %}
    <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-gradient-to-r from-gold-100 to-gold-200 text-gold-800">Pending</span>
{% elif status == 'in_progress' %}
    <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800">In Progress</span>
{% elif status == 'ready' %}
    <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-gradient-to-r from-emerald-100 to-emerald-200 text-emerald-800">Ready</span>
{% elif status == 'claimed' or status == 'completed' %}
    <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800">{{ status|title }}</span>
{% else %}
    <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800">{{ status|title }}</span>
{% endif %}
{% endwith %}
{% endblock %}

{% comment %} Table Component {% endcomment %}
{% block data_table %}
<div class="overflow-hidden rounded-xl border border-gray-200">
    <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gradient-to-r from-emerald-50 to-emerald-100">
            <tr>
                {% for header in headers %}
                <th class="px-6 py-4 text-left text-xs font-semibold text-emerald-700 uppercase tracking-wider">{{ header }}</th>
                {% endfor %}
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% block table_rows %}
            {% endblock %}
        </tbody>
    </table>
</div>
{% endblock %}

{% comment %} Empty State Component {% endcomment %}
{% block empty_state %}
<div class="text-center py-12">
    <div class="w-16 h-16 bg-gradient-to-br from-emerald-100 to-emerald-200 rounded-full flex items-center justify-center mx-auto mb-4">
        {% block empty_icon %}
        <svg class="w-8 h-8 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        {% endblock %}
    </div>
    <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ title|default:"No Data Available" }}</h3>
    <p class="text-gray-500 mb-4">{{ description|default:"No items found." }}</p>
    {% if action_url %}
    <a href="{{ action_url }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-emerald-700 bg-emerald-100 hover:bg-emerald-200 transition-colors duration-200">
        {{ action_text|default:"Take Action" }}
    </a>
    {% endif %}
</div>
{% endblock %}
