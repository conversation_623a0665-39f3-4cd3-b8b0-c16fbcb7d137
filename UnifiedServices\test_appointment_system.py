#!/usr/bin/env python
"""
Test script to verify the appointment system functionality
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UnifiedServices.settings')
django.setup()

from django.contrib.auth.models import User
from appointments.models import UserProfile, Department, Service, Appointment

def test_appointment_creation():
    """Test creating an appointment for student1"""
    print("Testing appointment creation...")

    # Get student user
    try:
        student = User.objects.get(username='student1')
        print(f"✓ Found student: {student.get_full_name()}")
    except User.DoesNotExist:
        print("✗ Student user not found")
        return False

    # Get a service
    try:
        service = Service.objects.filter(is_active=True).first()
        print(f"✓ Found service: {service.name} ({service.department.name})")
    except:
        print("✗ No active services found")
        return False

    # Create appointment
    try:
        appointment = Appointment.objects.create(
            student=student,
            service=service,
            notes="Test appointment created via script"
        )
        print(f"✓ Created appointment: {appointment.appointment_id}")
        print(f"  Status: {appointment.get_status_display()}")
        print(f"  Requirements: {appointment.requirements.count()}")
        print(f"  Ready for QR: {appointment.is_ready_for_qr}")
        return appointment
    except Exception as e:
        print(f"✗ Failed to create appointment: {e}")
        return False


def test_qr_generation():
    """Test QR code generation"""
    print("\nTesting QR code generation...")

    # Get an appointment
    appointment = Appointment.objects.first()
    if not appointment:
        print("✗ No appointments found")
        return False

    print(f"✓ Testing with appointment: {appointment.appointment_id}")

    # Mark all requirements as completed
    for req in appointment.requirements.all():
        req.is_completed = True
        req.save()
        print(f"  ✓ Marked requirement '{req.requirement.name}' as completed")

    # Check if ready for QR
    print(f"  Ready for QR: {appointment.is_ready_for_qr}")

    if appointment.is_ready_for_qr:
        try:
            qr_base64 = appointment.generate_qr_code()
            print(f"  ✓ QR code generated successfully")
            print(f"  Status updated to: {appointment.get_status_display()}")
            print(f"  QR hash: {appointment.qr_hash[:20]}...")
            return True
        except Exception as e:
            print(f"  ✗ Failed to generate QR code: {e}")
            return False
    else:
        print("  ✗ Appointment not ready for QR code")
        return False

def test_system_data():
    """Test that sample data is properly loaded"""
    print("\nTesting system data...")
    
    # Check departments
    dept_count = Department.objects.count()
    print(f"✓ Departments: {dept_count}")
    
    # Check services
    service_count = Service.objects.count()
    print(f"✓ Services: {service_count}")
    
    # Check users
    user_count = User.objects.count()
    print(f"✓ Users: {user_count}")
    
    # Check appointments
    appointment_count = Appointment.objects.count()
    print(f"✓ Appointments: {appointment_count}")
    
    return True

def main():
    print("=== JHCSC Unified Student Services - System Test ===\n")

    # Test system data
    test_system_data()

    # Test appointment creation
    appointment = test_appointment_creation()

    if appointment:
        # Test QR code generation
        qr_success = test_qr_generation()

        if qr_success:
            print("\n✓ All tests passed!")
        else:
            print("\n✗ QR code generation test failed.")

    print("\n=== Test Complete ===")
    print("You can now test the web interface at: http://127.0.0.1:8000/")
    print("Login credentials:")
    print("  Student: student1 / password123")
    print("  Registrar: registrar1 / password123")
    print("  Accounting: accounting1 / password123")
    print("  Admin: admin1 / password123")
    print("\nNew QR Code Features:")
    print("  - Visit appointment details to generate QR codes")
    print("  - Use QR scanner at /appointments/qr/scanner/ (staff only)")

if __name__ == "__main__":
    main()
