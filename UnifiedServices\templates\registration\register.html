<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Account - JHCSC Unified Student Services</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Custom Tailwind Config for Emerald & Gold Theme -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        emerald: {
                            50: '#ecfdf5',
                            100: '#d1fae5',
                            200: '#a7f3d0',
                            300: '#6ee7b7',
                            400: '#34d399',
                            500: '#10b981',
                            600: '#059669',
                            700: '#047857',
                            800: '#065f46',
                            900: '#064e3b',
                            950: '#022c22'
                        },
                        gold: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f'
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.8s ease-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                        'float': 'float 6s ease-in-out infinite',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' }
                        }
                    }
                }
            }
        }
    </script>
    
    <style>
        [x-cloak] { display: none !important; }
        
        .gradient-bg {
            background: linear-gradient(135deg, #059669 0%, #10b981 50%, #f59e0b 100%);
        }
        
        .glass-effect {
            backdrop-filter: blur(16px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .step-indicator {
            transition: all 0.3s ease;
        }
        
        .step-indicator.active {
            background: linear-gradient(135deg, #059669, #10b981);
            color: white;
        }
        
        .step-indicator.completed {
            background: linear-gradient(135deg, #10b981, #34d399);
            color: white;
        }
    </style>
</head>
<body class="h-full gradient-bg">
    <div class="flex min-h-full flex-col justify-center py-8 sm:px-6 lg:px-8 animate-fade-in">
        <!-- Floating Background Elements -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <div class="absolute top-1/4 left-1/4 w-32 h-32 bg-white/10 rounded-full animate-float"></div>
            <div class="absolute top-3/4 right-1/4 w-24 h-24 bg-gold-300/20 rounded-full animate-float" style="animation-delay: -2s;"></div>
            <div class="absolute top-1/2 right-1/3 w-16 h-16 bg-emerald-300/20 rounded-full animate-float" style="animation-delay: -4s;"></div>
        </div>
        
        <div class="sm:mx-auto sm:w-full sm:max-w-lg relative z-10">
            <div class="mx-auto h-20 w-20 flex items-center justify-center rounded-3xl bg-gradient-to-br from-white to-gray-50 shadow-2xl animate-slide-up">
                <svg class="h-12 w-12 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M18 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zM3 19.235v-.11a6.375 6.375 0 0112.75 0v.109A12.318 12.318 0 019.374 21c-2.331 0-4.512-.645-6.374-1.766z" />
                </svg>
            </div>
            <h2 class="mt-8 text-center text-4xl font-bold tracking-tight text-white animate-slide-up">
                Join Our Community
            </h2>
            <p class="mt-3 text-center text-lg text-white/90 animate-slide-up">
                Create your JHCSC Student Services account
            </p>
        </div>

        <div class="mt-10 sm:mx-auto sm:w-full sm:max-w-lg relative z-10">
            <div class="glass-effect py-10 px-8 shadow-2xl rounded-3xl border border-white/20 animate-slide-up">
                {% if messages %}
                    {% for message in messages %}
                    <div class="rounded-2xl p-4 mb-6 {% if message.tags == 'error' %}bg-gradient-to-r from-red-50 to-red-100 border-2 border-red-200{% else %}bg-gradient-to-r from-emerald-50 to-emerald-100 border-2 border-emerald-200{% endif %} animate-slide-up">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                {% if message.tags == 'error' %}
                                <svg class="h-5 w-5 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                {% else %}
                                <svg class="h-5 w-5 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                {% endif %}
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium {% if message.tags == 'error' %}text-red-800{% else %}text-emerald-800{% endif %}">{{ message }}</p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% endif %}

                <form class="space-y-6" method="post" x-data="{ 
                    showPassword: false, 
                    showConfirmPassword: false,
                    password: '',
                    confirmPassword: '',
                    passwordsMatch: true,
                    checkPasswords() {
                        this.passwordsMatch = this.password === this.confirmPassword || this.confirmPassword === '';
                    }
                }">
                    {% csrf_token %}
                    
                    <!-- Personal Information Section -->
                    <div class="space-y-6">
                        <div class="text-center">
                            <h3 class="text-lg font-semibold text-gray-800 mb-2">Personal Information</h3>
                            <div class="w-16 h-1 bg-gradient-to-r from-emerald-500 to-gold-500 rounded-full mx-auto"></div>
                        </div>
                        
                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <div>
                                <label for="first_name" class="block text-sm font-semibold text-gray-700 mb-2">First Name</label>
                                <div class="relative">
                                    <input id="first_name" name="first_name" type="text" required 
                                           class="block w-full px-4 py-3 text-base border-2 border-gray-200 rounded-xl placeholder-gray-400 bg-white/80 backdrop-blur-sm focus:border-emerald-500 focus:ring-emerald-500 focus:bg-white transition-all duration-200 shadow-sm hover:shadow-md"
                                           placeholder="John">
                                </div>
                            </div>
                            
                            <div>
                                <label for="last_name" class="block text-sm font-semibold text-gray-700 mb-2">Last Name</label>
                                <div class="relative">
                                    <input id="last_name" name="last_name" type="text" required 
                                           class="block w-full px-4 py-3 text-base border-2 border-gray-200 rounded-xl placeholder-gray-400 bg-white/80 backdrop-blur-sm focus:border-emerald-500 focus:ring-emerald-500 focus:bg-white transition-all duration-200 shadow-sm hover:shadow-md"
                                           placeholder="Doe">
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">Email Address</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <input id="email" name="email" type="email" required 
                                       class="block w-full pl-12 pr-4 py-3 text-base border-2 border-gray-200 rounded-xl placeholder-gray-400 bg-white/80 backdrop-blur-sm focus:border-emerald-500 focus:ring-emerald-500 focus:bg-white transition-all duration-200 shadow-sm hover:shadow-md"
                                       placeholder="<EMAIL>">
                            </div>
                        </div>
                        
                        <div>
                            <label for="student_id" class="block text-sm font-semibold text-gray-700 mb-2">Student ID</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
                                    </svg>
                                </div>
                                <input id="student_id" name="student_id" type="text" required 
                                       class="block w-full pl-12 pr-4 py-3 text-base border-2 border-gray-200 rounded-xl placeholder-gray-400 bg-white/80 backdrop-blur-sm focus:border-emerald-500 focus:ring-emerald-500 focus:bg-white transition-all duration-200 shadow-sm hover:shadow-md"
                                       placeholder="2024-12345">
                            </div>
                        </div>
                    </div>

                    <!-- Account Security Section -->
                    <div class="space-y-6 pt-6 border-t border-gray-200">
                        <div class="text-center">
                            <h3 class="text-lg font-semibold text-gray-800 mb-2">Account Security</h3>
                            <div class="w-16 h-1 bg-gradient-to-r from-emerald-500 to-gold-500 rounded-full mx-auto"></div>
                        </div>
                        
                        <div>
                            <label for="username" class="block text-sm font-semibold text-gray-700 mb-2">Username</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                                <input id="username" name="username" type="text" required 
                                       class="block w-full pl-12 pr-4 py-3 text-base border-2 border-gray-200 rounded-xl placeholder-gray-400 bg-white/80 backdrop-blur-sm focus:border-emerald-500 focus:ring-emerald-500 focus:bg-white transition-all duration-200 shadow-sm hover:shadow-md"
                                       placeholder="Choose a unique username">
                            </div>
                        </div>
                        
                        <div>
                            <label for="password1" class="block text-sm font-semibold text-gray-700 mb-2">Password</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                    </svg>
                                </div>
                                <input id="password1" name="password1" :type="showPassword ? 'text' : 'password'" required 
                                       x-model="password" @input="checkPasswords()"
                                       class="block w-full pl-12 pr-12 py-3 text-base border-2 border-gray-200 rounded-xl placeholder-gray-400 bg-white/80 backdrop-blur-sm focus:border-emerald-500 focus:ring-emerald-500 focus:bg-white transition-all duration-200 shadow-sm hover:shadow-md"
                                       placeholder="Create a strong password">
                                <div class="absolute inset-y-0 right-0 pr-4 flex items-center">
                                    <button type="button" @click="showPassword = !showPassword" class="text-gray-400 hover:text-gray-600 transition-colors duration-200">
                                        <svg x-show="!showPassword" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                        </svg>
                                        <svg x-show="showPassword" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" x-cloak>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <label for="password2" class="block text-sm font-semibold text-gray-700 mb-2">Confirm Password</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <input id="password2" name="password2" :type="showConfirmPassword ? 'text' : 'password'" required 
                                       x-model="confirmPassword" @input="checkPasswords()"
                                       :class="!passwordsMatch ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'border-gray-200 focus:border-emerald-500 focus:ring-emerald-500'"
                                       class="block w-full pl-12 pr-12 py-3 text-base rounded-xl placeholder-gray-400 bg-white/80 backdrop-blur-sm focus:bg-white transition-all duration-200 shadow-sm hover:shadow-md"
                                       placeholder="Confirm your password">
                                <div class="absolute inset-y-0 right-0 pr-4 flex items-center">
                                    <button type="button" @click="showConfirmPassword = !showConfirmPassword" class="text-gray-400 hover:text-gray-600 transition-colors duration-200">
                                        <svg x-show="!showConfirmPassword" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                        </svg>
                                        <svg x-show="showConfirmPassword" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" x-cloak>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <p x-show="!passwordsMatch && confirmPassword !== ''" class="mt-2 text-sm text-red-600" x-cloak>
                                Passwords do not match
                            </p>
                        </div>
                    </div>

                    <!-- Terms and Submit -->
                    <div class="space-y-6 pt-6 border-t border-gray-200">
                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input id="terms" name="terms" type="checkbox" required
                                       class="h-5 w-5 rounded-lg border-2 border-gray-300 text-emerald-600 focus:ring-emerald-500 focus:ring-offset-0 transition-colors duration-200">
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="terms" class="font-medium text-gray-700">
                                    I agree to the <a href="#" class="text-emerald-600 hover:text-emerald-700 font-semibold">Terms of Service</a> and <a href="#" class="text-emerald-600 hover:text-emerald-700 font-semibold">Privacy Policy</a>
                                </label>
                            </div>
                        </div>

                        <div>
                            <button type="submit" 
                                    class="group relative w-full flex justify-center py-4 px-6 border border-transparent text-lg font-semibold rounded-2xl text-white bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                                <span class="absolute left-0 inset-y-0 flex items-center pl-4">
                                    <svg class="h-6 w-6 text-emerald-300 group-hover:text-emerald-200 transition-colors duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zM3 19.235v-.11a6.375 6.375 0 0112.75 0v.109A12.318 12.318 0 019.374 21c-2.331 0-4.512-.645-6.374-1.766z" />
                                    </svg>
                                </span>
                                Create My Account
                            </button>
                        </div>
                    </div>
                </form>

                <div class="mt-8">
                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-200"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="bg-white px-4 text-gray-500 font-medium">Already have an account?</span>
                        </div>
                    </div>

                    <div class="mt-6 text-center">
                        <a href="{% url 'login' %}" class="text-emerald-600 hover:text-emerald-700 font-semibold text-lg transition-colors duration-200">
                            Sign in to your account →
                        </a>
                    </div>
                   
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="mt-8 text-center">
            <p class="text-white/80 text-sm">
                © 2025 Unified Services. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
