"""
Test cases for security features
"""

from django.test import <PERSON><PERSON><PERSON>, Client, RequestFactory
from django.contrib.auth.models import User
from django.core.files.uploadedfile import SimpleUploadedFile
from django.utils import timezone
from unittest.mock import patch, MagicMock
import json

from appointments.models import (
    UserProfile, Department, Service, Appointment, 
    SecurityEvent, AuditLog, FileAccessLog
)
from appointments.security import (
    SecurityMiddleware, FileSecurityManager, QRSecurityManager
)
from appointments.audit import AuditLogger


class SecurityMiddlewareTest(TestCase):
    """Test SecurityMiddleware functionality"""
    
    def setUp(self):
        self.factory = RequestFactory()
        self.middleware = SecurityMiddleware(lambda request: None)
        
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_sql_injection_detection(self):
        """Test SQL injection attempt detection"""
        # Create request with SQL injection payload
        request = self.factory.post('/test/', {
            'search': "'; DROP TABLE users; --"
        })
        request.user = self.user
        request.session = {}
        
        # Process request through middleware
        response = self.middleware(request)
        
        # Verify security event was created
        events = SecurityEvent.objects.filter(
            event_type='sql_injection',
            user=self.user
        )
        self.assertTrue(events.exists())
    
    def test_xss_attempt_detection(self):
        """Test XSS attempt detection"""
        request = self.factory.post('/test/', {
            'comment': '<script>alert("xss")</script>'
        })
        request.user = self.user
        request.session = {}
        
        response = self.middleware(request)
        
        # Verify security event was created
        events = SecurityEvent.objects.filter(
            event_type='xss_attempt',
            user=self.user
        )
        self.assertTrue(events.exists())
    
    def test_brute_force_detection(self):
        """Test brute force attack detection"""
        # Simulate multiple rapid requests
        for i in range(15):  # Exceed rate limit
            request = self.factory.post('/login/', {
                'username': 'testuser',
                'password': 'wrongpass'
            })
            request.user = self.user
            request.session = {}
            
            self.middleware(request)
        
        # Verify brute force event was created
        events = SecurityEvent.objects.filter(
            event_type='brute_force'
        )
        self.assertTrue(events.exists())
    
    def test_rate_limiting(self):
        """Test rate limiting functionality"""
        # Make requests within rate limit
        for i in range(5):
            request = self.factory.get('/api/test/')
            request.user = self.user
            request.session = {}
            
            response = self.middleware(request)
            self.assertIsNone(response)  # Should pass through
        
        # Exceed rate limit
        for i in range(10):
            request = self.factory.get('/api/test/')
            request.user = self.user
            request.session = {}
            
            response = self.middleware(request)
        
        # Should create security event
        events = SecurityEvent.objects.filter(
            event_type='brute_force'
        )
        self.assertTrue(events.exists())


class FileSecurityManagerTest(TestCase):
    """Test FileSecurityManager functionality"""
    
    def test_allowed_file_validation(self):
        """Test validation of allowed file types"""
        # Test valid PDF file
        pdf_file = SimpleUploadedFile(
            "document.pdf",
            b"%PDF-1.4 valid pdf content",
            content_type="application/pdf"
        )
        
        errors = FileSecurityManager.validate_file(pdf_file)
        self.assertEqual(len(errors), 0)
    
    def test_disallowed_file_validation(self):
        """Test validation rejects disallowed file types"""
        # Test executable file
        exe_file = SimpleUploadedFile(
            "malware.exe",
            b"MZ executable content",
            content_type="application/x-executable"
        )
        
        errors = FileSecurityManager.validate_file(exe_file)
        self.assertGreater(len(errors), 0)
        self.assertIn('not allowed', ' '.join(errors).lower())
    
    def test_file_size_validation(self):
        """Test file size validation"""
        # Create oversized file (simulate 20MB)
        large_content = b"x" * (20 * 1024 * 1024)
        large_file = SimpleUploadedFile(
            "large.pdf",
            large_content,
            content_type="application/pdf"
        )
        
        errors = FileSecurityManager.validate_file(large_file)
        self.assertGreater(len(errors), 0)
        self.assertIn('size', ' '.join(errors).lower())
    
    def test_malicious_content_detection(self):
        """Test malicious content detection"""
        # File with suspicious script content
        malicious_file = SimpleUploadedFile(
            "document.pdf",
            b"<script>alert('xss')</script>",
            content_type="application/pdf"
        )
        
        errors = FileSecurityManager.validate_file(malicious_file)
        self.assertGreater(len(errors), 0)
        self.assertIn('suspicious', ' '.join(errors).lower())
    
    def test_file_access_logging(self):
        """Test file access logging"""
        user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        department = Department.objects.create(
            name='Test Dept',
            description='Test'
        )
        
        service = Service.objects.create(
            name='Test Service',
            department=department
        )
        
        appointment = Appointment.objects.create(
            student=user,
            service=service
        )
        
        # Mock request
        request = MagicMock()
        request.user = user
        request.META = {'REMOTE_ADDR': '***********'}
        
        FileSecurityManager.log_file_access(
            user=user,
            appointment=appointment,
            file_path='/test/file.pdf',
            file_name='file.pdf',
            access_type='upload',
            request=request,
            success=True
        )
        
        # Verify log was created
        logs = FileAccessLog.objects.filter(
            user=user,
            appointment=appointment,
            access_type='upload'
        )
        self.assertTrue(logs.exists())


class QRSecurityManagerTest(TestCase):
    """Test QRSecurityManager functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        self.department = Department.objects.create(
            name='Test Dept',
            description='Test'
        )
        
        self.service = Service.objects.create(
            name='Test Service',
            department=self.department
        )
        
        self.appointment = Appointment.objects.create(
            student=self.user,
            service=self.service
        )
    
    def test_qr_integrity_validation(self):
        """Test QR code integrity validation"""
        # Generate valid QR code
        qr_data, _ = self.appointment.generate_qr_code()
        qr_hash = qr_data['hash']
        
        # Test valid QR code
        is_valid = QRSecurityManager.validate_qr_integrity(qr_data, qr_hash)
        self.assertTrue(is_valid)
        
        # Test tampered QR code
        tampered_data = qr_data.copy()
        tampered_data['student_id'] = 999
        
        is_valid = QRSecurityManager.validate_qr_integrity(tampered_data, qr_hash)
        self.assertFalse(is_valid)
    
    def test_qr_scan_logging(self):
        """Test QR code scan logging"""
        request = MagicMock()
        request.user = self.user
        request.META = {'REMOTE_ADDR': '***********'}
        
        QRSecurityManager.log_qr_scan(
            user=self.user,
            appointment=self.appointment,
            request=request,
            success=True,
            message='QR code validated successfully'
        )
        
        # Verify audit log was created
        logs = AuditLog.objects.filter(
            user=self.user,
            action='qr_validated'
        )
        self.assertTrue(logs.exists())
    
    def test_qr_tampering_detection(self):
        """Test QR code tampering detection and logging"""
        request = MagicMock()
        request.user = self.user
        request.META = {'REMOTE_ADDR': '***********'}
        
        QRSecurityManager.log_qr_scan(
            user=self.user,
            appointment=self.appointment,
            request=request,
            success=False,
            message='QR code integrity check failed'
        )
        
        # Verify security event was created
        events = SecurityEvent.objects.filter(
            event_type='qr_tampering',
            user=self.user
        )
        self.assertTrue(events.exists())


class AuditLoggerTest(TestCase):
    """Test AuditLogger functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
    
    def test_user_action_logging(self):
        """Test user action logging"""
        request = MagicMock()
        request.user = self.user
        request.META = {'REMOTE_ADDR': '***********'}
        request.path = '/test/'
        request.method = 'GET'
        
        AuditLogger.log_user_action(
            user=self.user,
            action='test_action',
            description='Test action performed',
            request=request
        )
        
        # Verify audit log was created
        logs = AuditLog.objects.filter(
            user=self.user,
            action='test_action'
        )
        self.assertTrue(logs.exists())
    
    def test_security_violation_logging(self):
        """Test security violation logging"""
        request = MagicMock()
        request.user = self.user
        request.META = {'REMOTE_ADDR': '***********'}
        
        AuditLogger.log_security_violation(
            user=self.user,
            violation_type='test_violation',
            description='Test security violation',
            request=request,
            severity='high'
        )
        
        # Verify both audit log and security event were created
        audit_logs = AuditLog.objects.filter(
            user=self.user,
            action='security_violation'
        )
        self.assertTrue(audit_logs.exists())
        
        security_events = SecurityEvent.objects.filter(
            user=self.user,
            event_type='test_violation',
            severity='high'
        )
        self.assertTrue(security_events.exists())
    
    def test_system_event_logging(self):
        """Test system event logging"""
        AuditLogger.log_system_event(
            event_type='system_startup',
            description='System started successfully'
        )
        
        # Verify system audit log was created
        logs = AuditLog.objects.filter(
            user=None,  # System events have no user
            action='system_startup'
        )
        self.assertTrue(logs.exists())
    
    @patch('appointments.audit.timezone')
    def test_audit_report_generation(self, mock_timezone):
        """Test audit report generation"""
        # Mock current time
        mock_timezone.now.return_value = timezone.now()
        
        # Create test audit logs
        AuditLog.objects.create(
            user=self.user,
            action='login',
            description='User logged in'
        )
        
        AuditLog.objects.create(
            user=self.user,
            action='logout',
            description='User logged out'
        )
        
        # Generate report
        start_date = timezone.now().date()
        end_date = timezone.now().date()
        
        report = AuditLogger.generate_audit_report(start_date, end_date)
        
        # Verify report structure
        self.assertIn('total_events', report)
        self.assertIn('events_by_action', report)
        self.assertIn('events_by_user', report)
        self.assertIn('security_events', report)
        
        # Verify data
        self.assertEqual(report['total_events'], 2)
        self.assertIn('login', report['events_by_action'])
        self.assertIn('logout', report['events_by_action'])


class IntegrationSecurityTest(TestCase):
    """Integration tests for security features"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        self.department = Department.objects.create(
            name='Test Dept',
            description='Test'
        )
        
        self.service = Service.objects.create(
            name='Test Service',
            department=self.department
        )
    
    def test_end_to_end_security_logging(self):
        """Test end-to-end security logging"""
        # Login (should create audit log)
        self.client.login(username='testuser', password='testpass123')
        
        # Create appointment (should create audit log)
        appointment = Appointment.objects.create(
            student=self.user,
            service=self.service
        )
        
        # Generate QR code (should create audit log)
        qr_data, _ = appointment.generate_qr_code()
        
        # Verify audit logs were created
        login_logs = AuditLog.objects.filter(
            user=self.user,
            action='login'
        )
        self.assertTrue(login_logs.exists())
        
        appointment_logs = AuditLog.objects.filter(
            user=self.user,
            action='appointment_created'
        )
        self.assertTrue(appointment_logs.exists())
        
        qr_logs = AuditLog.objects.filter(
            user=self.user,
            action='qr_generated'
        )
        self.assertTrue(qr_logs.exists())
    
    def test_security_event_escalation(self):
        """Test security event escalation"""
        # Create multiple failed login attempts
        for i in range(5):
            self.client.post('/login/', {
                'username': 'testuser',
                'password': 'wrongpassword'
            })
        
        # Should create security events
        failed_login_events = SecurityEvent.objects.filter(
            event_type='multiple_failed_logins'
        )
        
        # Verify escalation occurred
        if failed_login_events.exists():
            event = failed_login_events.first()
            self.assertIn(event.severity, ['medium', 'high', 'critical'])
    
    def test_comprehensive_threat_detection(self):
        """Test comprehensive threat detection"""
        self.client.login(username='testuser', password='testpass123')
        
        # Test various attack vectors
        attack_payloads = [
            {'type': 'sql', 'data': "'; DROP TABLE users; --"},
            {'type': 'xss', 'data': '<script>alert("xss")</script>'},
            {'type': 'path_traversal', 'data': '../../../etc/passwd'},
        ]
        
        for payload in attack_payloads:
            response = self.client.post('/test/', {
                'input': payload['data']
            })
        
        # Verify security events were created
        total_events = SecurityEvent.objects.count()
        self.assertGreater(total_events, 0)
