from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
import uuid
import hashlib
import hmac
import json
import qrcode
import base64
from io import BytesIO
from PIL import Image
from django.conf import settings
from django.core.files.base import ContentFile


class UserProfile(models.Model):
    """Extended user profile with role-based access control"""
    ROLE_CHOICES = [
        ('student', 'Student'),
        ('office_staff', 'Office Staff'),
        ('admin', 'Administrator'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE)
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='student')
    department = models.ForeignKey('Department', null=True, blank=True, on_delete=models.SET_NULL)
    student_id = models.CharField(max_length=20, null=True, blank=True)
    phone_number = models.CharField(max_length=15, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.get_full_name()} ({self.get_role_display()})"

    class Meta:
        verbose_name = "User Profile"
        verbose_name_plural = "User Profiles"


class Department(models.Model):
    """Academic and administrative departments"""
    name = models.CharField(max_length=100)  # Registrar, Accounting, Guidance
    code = models.CharField(max_length=10, unique=True)
    description = models.TextField()
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']


class Service(models.Model):
    """Services offered by departments"""
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='services')
    name = models.CharField(max_length=200)  # Certificate of Good Moral
    description = models.TextField()
    processing_time = models.CharField(max_length=50)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.department.name} - {self.name}"

    class Meta:
        ordering = ['department__name', 'name']


class ServiceRequirement(models.Model):
    """Requirements for each service"""
    service = models.ForeignKey(Service, on_delete=models.CASCADE, related_name='requirements')
    name = models.CharField(max_length=200)
    description = models.TextField()
    is_required = models.BooleanField(default=True)
    requires_upload = models.BooleanField(default=False)
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.service.name} - {self.name}"

    class Meta:
        ordering = ['service', 'order', 'name']


class Appointment(models.Model):
    """Student service appointments"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('ready', 'Ready for Pickup'),
        ('claimed', 'Claimed'),
        ('cancelled', 'Cancelled'),
    ]

    appointment_id = models.CharField(max_length=20, unique=True, editable=False)
    student = models.ForeignKey(User, on_delete=models.CASCADE, related_name='appointments')
    service = models.ForeignKey(Service, on_delete=models.CASCADE)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    assigned_staff = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_appointments')
    notes = models.TextField(blank=True)
    qr_code = models.ImageField(upload_to='qr_codes/', null=True, blank=True)
    qr_hash = models.CharField(max_length=64, null=True, blank=True)
    claimed_at = models.DateTimeField(null=True, blank=True)
    claimed_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL, related_name='claimed_appointments')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.appointment_id:
            # Generate unique appointment ID
            self.appointment_id = f"APP{timezone.now().strftime('%Y%m%d')}{str(uuid.uuid4())[:8].upper()}"
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.appointment_id} - {self.student.get_full_name()} - {self.service.name}"

    @property
    def is_ready_for_qr(self):
        """Check if all requirements are completed and QR code can be generated"""
        return all(req.is_completed for req in self.requirements.all())

    def generate_qr_hash(self):
        """Generate HMAC verification hash for QR code"""
        hash_data = f"{self.appointment_id}:{self.student.id}:{self.service.id}:{self.created_at.isoformat()}"
        return hmac.new(
            settings.SECRET_KEY.encode(),
            hash_data.encode(),
            hashlib.sha256
        ).hexdigest()

    def generate_qr_code(self):
        """Generate QR code for appointment when ready"""
        if not self.is_ready_for_qr:
            return False

        # Generate verification hash
        verification_hash = self.generate_qr_hash()
        self.qr_hash = verification_hash

        # Create QR code data
        qr_data = {
            'appointment_id': self.appointment_id,
            'student_id': str(self.student.id),
            'created_at': self.created_at.isoformat(),
            'hash': verification_hash,
            'version': '1.1'  # Version updated for new data structure
        }

        # Generate QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(json.dumps(qr_data))
        qr.make(fit=True)

        # Create image
        qr_image = qr.make_image(fill_color="black", back_color="white")

        # Convert to base64 for storage
        buffer = BytesIO()
        qr_image.save(buffer, format='PNG')
        qr_base64 = base64.b64encode(buffer.getvalue()).decode()

        # Save as file
        qr_filename = f"qr_{self.appointment_id}.png"
        qr_file = ContentFile(buffer.getvalue(), name=qr_filename)
        self.qr_code.save(qr_filename, qr_file, save=False)

        # Update status to ready
        if self.status != 'ready':
            self.status = 'ready'

        self.save()
        return qr_base64

    def validate_qr_code(self, scanned_data):
        """Validate scanned QR code data"""
        try:
            qr_data = json.loads(scanned_data)

            # Check required fields
            required_fields = ['appointment_id', 'student_id', 'hash', 'version']
            if not all(field in qr_data for field in required_fields):
                return False, "Invalid QR code format"

            # Verify appointment ID matches
            if qr_data['appointment_id'] != self.appointment_id:
                return False, "Appointment ID mismatch"

            # Verify student ID matches
            if qr_data['student_id'] != str(self.student.id):
                return False, "Student ID mismatch"

            # Verify hash
            expected_hash = self.generate_qr_hash()
            if qr_data['hash'] != expected_hash:
                return False, "Invalid verification hash"

            # Check if already claimed
            if self.status == 'claimed':
                return False, "Appointment already claimed"

            # Check if ready for pickup
            if self.status != 'ready':
                return False, "Appointment not ready for pickup"

            return True, "Valid QR code"

        except (json.JSONDecodeError, KeyError) as e:
            return False, f"Invalid QR code data: {str(e)}"

    def claim_appointment(self, claimed_by_user):
        """Mark appointment as claimed"""
        if self.status != 'ready':
            return False, "Appointment not ready for claiming"

        self.status = 'claimed'
        self.claimed_at = timezone.now()
        self.claimed_by = claimed_by_user
        self.save()

        return True, "Appointment claimed successfully"

    @property
    def completed_requirements_count(self):
        """Get count of completed requirements"""
        return self.requirements.filter(is_completed=True).count()

    @property
    def completion_percentage(self):
        """Get completion percentage for progress bars"""
        total = self.requirements.count()
        if total == 0:
            return 0
        completed = self.completed_requirements_count
        return int((completed / total) * 100)

    def update_status(self):
        """
        Automatically update appointment status based on requirement completion ratio.

        Status Logic:
        - "In Progress": When staff is actively working on requirements OR when only some
          requirements are completed (partial completion, e.g., 1 out of 3 requirements done)
        - "Ready for Pickup": When ALL requirements are marked as complete
          (100% completion, e.g., 3 out of 3 requirements done)
        """
        # Don't auto-update if already claimed or cancelled
        if self.status in ['claimed', 'cancelled']:
            return

        total_requirements = self.requirements.count()
        completed_requirements = self.completed_requirements_count

        # If no requirements exist, keep current status
        if total_requirements == 0:
            return

        # Calculate completion ratio
        completion_ratio = completed_requirements / total_requirements

        if completion_ratio == 1.0:
            # All requirements completed - mark as ready for pickup
            if self.status != 'ready':
                self.status = 'ready'
                self.save()
        elif completion_ratio > 0:
            # Some requirements completed - mark as in progress
            if self.status == 'pending':
                self.status = 'in_progress'
                self.save()
        # If completion_ratio == 0 and status is 'in_progress', we might want to revert to 'pending'
        # but this could be confusing for staff, so we'll leave it as is

    class Meta:
        ordering = ['-created_at']


class AppointmentRequirement(models.Model):
    """Individual requirement tracking for appointments"""
    appointment = models.ForeignKey(Appointment, on_delete=models.CASCADE, related_name='requirements')
    requirement = models.ForeignKey(ServiceRequirement, on_delete=models.CASCADE)
    is_completed = models.BooleanField(default=False)
    completed_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL)
    completed_at = models.DateTimeField(null=True, blank=True)
    uploaded_file = models.FileField(upload_to='documents/', null=True, blank=True)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        status = "✓" if self.is_completed else "○"
        return f"{status} {self.appointment.appointment_id} - {self.requirement.name}"

    class Meta:
        unique_together = ['appointment', 'requirement']
        ordering = ['requirement__order', 'requirement__name']


class AuditLog(models.Model):
    """Comprehensive audit logging for all system activities"""
    ACTION_CHOICES = [
        ('login', 'User Login'),
        ('logout', 'User Logout'),
        ('login_failed', 'Failed Login Attempt'),
        ('password_changed', 'Password Changed'),
        ('appointment_created', 'Appointment Created'),
        ('appointment_updated', 'Appointment Updated'),
        ('appointment_viewed', 'Appointment Viewed'),
        ('appointment_deleted', 'Appointment Deleted'),
        ('requirement_completed', 'Requirement Completed'),
        ('requirement_uncompleted', 'Requirement Uncompleted'),
        ('file_uploaded', 'File Uploaded'),
        ('file_downloaded', 'File Downloaded'),
        ('file_deleted', 'File Deleted'),
        ('qr_generated', 'QR Code Generated'),
        ('qr_scanned', 'QR Code Scanned'),
        ('qr_verified', 'QR Code Verified'),
        ('document_claimed', 'Document Claimed'),
        ('user_created', 'User Created'),
        ('user_updated', 'User Updated'),
        ('user_deleted', 'User Deleted'),
        ('role_changed', 'User Role Changed'),
        ('permission_denied', 'Permission Denied'),
        ('admin_access', 'Admin Panel Access'),
        ('data_export', 'Data Export'),
        ('system_error', 'System Error'),
        ('security_violation', 'Security Violation'),
    ]

    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    action = models.CharField(max_length=30, choices=ACTION_CHOICES)
    description = models.TextField()
    appointment = models.ForeignKey(Appointment, null=True, blank=True, on_delete=models.SET_NULL)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    session_key = models.CharField(max_length=40, blank=True)
    request_path = models.CharField(max_length=500, blank=True)
    request_method = models.CharField(max_length=10, blank=True)
    response_status = models.IntegerField(null=True, blank=True)
    additional_data = models.TextField(blank=True, help_text="JSON formatted additional data")
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        user_info = self.user.username if self.user else "Anonymous"
        return f"{self.timestamp.strftime('%Y-%m-%d %H:%M:%S')} - {user_info} - {self.get_action_display()}"

    class Meta:
        ordering = ['-timestamp']
        verbose_name = "Audit Log"
        verbose_name_plural = "Audit Logs"
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['action', 'timestamp']),
            models.Index(fields=['ip_address', 'timestamp']),
        ]

    @classmethod
    def log_action(cls, user=None, action=None, description=None, appointment=None,
                   request=None, additional_data=None):
        """Utility method to create audit log entries"""
        log_data = {
            'user': user,
            'action': action,
            'description': description,
            'appointment': appointment,
        }

        if request:
            log_data.update({
                'ip_address': cls.get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'session_key': request.session.session_key or '',
                'request_path': request.path,
                'request_method': request.method,
            })

        if additional_data:
            log_data['additional_data'] = json.dumps(additional_data) if isinstance(additional_data, dict) else str(additional_data)

        return cls.objects.create(**log_data)

    @staticmethod
    def get_client_ip(request):
        """Extract client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class SecurityEvent(models.Model):
    """Track security-related events and potential threats"""
    EVENT_TYPES = [
        ('failed_login', 'Failed Login'),
        ('multiple_failed_logins', 'Multiple Failed Logins'),
        ('suspicious_activity', 'Suspicious Activity'),
        ('unauthorized_access', 'Unauthorized Access Attempt'),
        ('file_access_violation', 'File Access Violation'),
        ('qr_tampering', 'QR Code Tampering'),
        ('session_hijacking', 'Potential Session Hijacking'),
        ('brute_force', 'Brute Force Attack'),
        ('sql_injection', 'SQL Injection Attempt'),
        ('xss_attempt', 'XSS Attempt'),
    ]

    SEVERITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]

    event_type = models.CharField(max_length=30, choices=EVENT_TYPES)
    severity = models.CharField(max_length=10, choices=SEVERITY_LEVELS, default='medium')
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    description = models.TextField()
    request_path = models.CharField(max_length=500, blank=True)
    request_data = models.TextField(blank=True, help_text="JSON formatted request data")
    resolved = models.BooleanField(default=False)
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_security_events')
    resolved_at = models.DateTimeField(null=True, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.timestamp.strftime('%Y-%m-%d %H:%M:%S')} - {self.get_event_type_display()} ({self.get_severity_display()})"

    class Meta:
        ordering = ['-timestamp']
        verbose_name = "Security Event"
        verbose_name_plural = "Security Events"


class FileAccessLog(models.Model):
    """Track file access and downloads for security auditing"""
    ACCESS_TYPES = [
        ('upload', 'File Upload'),
        ('download', 'File Download'),
        ('view', 'File View'),
        ('delete', 'File Delete'),
        ('unauthorized', 'Unauthorized Access'),
    ]

    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    appointment = models.ForeignKey(Appointment, on_delete=models.CASCADE, null=True, blank=True)
    file_path = models.CharField(max_length=500)
    file_name = models.CharField(max_length=255)
    access_type = models.CharField(max_length=20, choices=ACCESS_TYPES)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    success = models.BooleanField(default=True)
    error_message = models.TextField(blank=True)
    file_size = models.BigIntegerField(null=True, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        user_info = self.user.username if self.user else "Anonymous"
        return f"{self.timestamp.strftime('%Y-%m-%d %H:%M:%S')} - {user_info} - {self.get_access_type_display()} - {self.file_name}"

    class Meta:
        ordering = ['-timestamp']
        verbose_name = "File Access Log"
        verbose_name_plural = "File Access Logs"


# Signal handlers for automatic audit logging
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver


@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    """Automatically create UserProfile when User is created"""
    if created:
        UserProfile.objects.create(user=instance)


@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    """Save UserProfile when User is saved"""
    if hasattr(instance, 'userprofile'):
        instance.userprofile.save()


class AppointmentNote(models.Model):
    """Internal notes for appointments"""
    NOTE_TYPES = [
        ('internal', 'Internal Note'),
        ('assignment', 'Assignment Note'),
        ('status_change', 'Status Change Note'),
        ('general', 'General Note'),
    ]

    appointment = models.ForeignKey(Appointment, on_delete=models.CASCADE, related_name='internal_notes')
    staff_member = models.ForeignKey(User, on_delete=models.CASCADE)
    content = models.TextField()
    note_type = models.CharField(max_length=20, choices=NOTE_TYPES, default='internal')
    important = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Note for {self.appointment.appointment_id} by {self.staff_member.get_full_name()}"


class AppointmentHistory(models.Model):
    """Audit trail for appointment changes"""
    appointment = models.ForeignKey(Appointment, on_delete=models.CASCADE, related_name='history')
    staff_member = models.ForeignKey(User, on_delete=models.CASCADE)
    action = models.CharField(max_length=255)
    details = models.TextField(blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-timestamp']
        verbose_name_plural = "Appointment histories"

    def __str__(self):
        return f"{self.appointment.appointment_id} - {self.action} by {self.staff_member.get_full_name()}"


@receiver(post_save, sender=Appointment)
def create_appointment_requirements(sender, instance, created, **kwargs):
    """Automatically create AppointmentRequirement objects when Appointment is created"""
    if created:
        for requirement in instance.service.requirements.all():
            AppointmentRequirement.objects.create(
                appointment=instance,
                requirement=requirement
            )
