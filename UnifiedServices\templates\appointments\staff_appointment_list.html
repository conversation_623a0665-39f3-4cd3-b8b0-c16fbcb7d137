{% extends 'base.html' %}
{% load static %}

{% block title %}Appointment Management - JHCSC Unified Student Services{% endblock %}

{% block page_title %}Appointment Management{% endblock %}

{% block extra_head %}
<!-- Custom Tailwind Config for Emerald & Gold Theme -->
<script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    emerald: {
                        50: '#ecfdf5',
                        100: '#d1fae5',
                        200: '#a7f3d0',
                        300: '#6ee7b7',
                        400: '#34d399',
                        500: '#10b981',
                        600: '#059669',
                        700: '#047857',
                        800: '#065f46',
                        900: '#064e3b',
                        950: '#022c22'
                    },
                    gold: {
                        50: '#fffbeb',
                        100: '#fef3c7',
                        200: '#fde68a',
                        300: '#fcd34d',
                        400: '#fbbf24',
                        500: '#f59e0b',
                        600: '#d97706',
                        700: '#b45309',
                        800: '#92400e',
                        900: '#78350f'
                    }
                },
                animation: {
                    'fade-in': 'fadeIn 0.6s ease-out',
                    'slide-up': 'slideUp 0.6s ease-out',
                },
                keyframes: {
                    fadeIn: {
                        '0%': { opacity: '0' },
                        '100%': { opacity: '1' }
                    },
                    slideUp: {
                        '0%': { opacity: '0', transform: 'translateY(20px)' },
                        '100%': { opacity: '1', transform: 'translateY(0)' }
                    }
                }
            }
        }
    }
</script>

<style>
    .card-hover {
        transition: all 0.3s ease;
    }
    .card-hover:hover {
        transform: translateY(-2px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    .hero-gradient {
        background: linear-gradient(135deg, #064e3b 0%, #047857 50%, #059669 100%);
    }
    .quick-action-btn {
        opacity: 0;
        transform: translateX(10px);
        transition: all 0.2s ease;
    }
    .group:hover .quick-action-btn {
        opacity: 1;
        transform: translateX(0);
    }
</style>

<!-- Include Modal System -->
<script src="{% static 'js/modal-system.js' %}"></script>
<script src="{% static 'js/appointment-management.js' %}"></script>
{% endblock %}

{% block mobile_nav %}
{% include 'navigation/staff_nav.html' %}
{% endblock %}

{% block desktop_nav %}
{% include 'navigation/staff_nav.html' %}
{% endblock %}

{% block content %}
<div class="animate-fade-in">
    <!-- Header Section -->
    <div class="hero-gradient rounded-2xl p-8 mb-8 text-white shadow-2xl relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <svg class="w-full h-full" viewBox="0 0 100 100" fill="none">
                <defs>
                    <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                        <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" stroke-width="0.5"/>
                    </pattern>
                </defs>
                <rect width="100" height="100" fill="url(#grid)" />
            </svg>
        </div>

        <div class="relative flex items-center justify-between">
            <div class="flex-1">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center mr-4">
                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold mb-1">{{ department.name }} Appointments</h1>
                        <p class="text-emerald-100 text-lg font-medium">Appointment Management Portal</p>
                    </div>
                </div>
                <p class="text-emerald-200 text-sm">Manage and review student service requests efficiently</p>
            </div>
            <div class="hidden lg:block">
                <div class="w-24 h-24 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                    <svg class="w-12 h-12 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8 animate-slide-up">
        <div class="bg-white overflow-hidden shadow-xl rounded-2xl border border-gray-100 card-hover group">
            <div class="p-6 relative">
                <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-gold-50 to-gold-100 rounded-bl-3xl opacity-50"></div>
                <div class="relative flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-14 h-14 bg-gradient-to-br from-gold-400 to-gold-500 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200">
                            <svg class="h-7 w-7 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-semibold text-gray-500 truncate uppercase tracking-wide">Pending</dt>
                            <dd class="text-3xl font-bold text-gray-900 mb-1">{{ pending_count }}</dd>
                            <dd class="text-xs text-gray-400">Awaiting processing</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-xl rounded-2xl border border-gray-100 card-hover group">
            <div class="p-6 relative">
                <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-50 to-blue-100 rounded-bl-3xl opacity-50"></div>
                <div class="relative flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-14 h-14 bg-gradient-to-br from-blue-400 to-blue-500 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200">
                            <svg class="h-7 w-7 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-semibold text-gray-500 truncate uppercase tracking-wide">In Progress</dt>
                            <dd class="text-3xl font-bold text-gray-900 mb-1">{{ in_progress_count }}</dd>
                            <dd class="text-xs text-gray-400">Currently processing</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-xl rounded-2xl border border-gray-100 card-hover group">
            <div class="p-6 relative">
                <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-bl-3xl opacity-50"></div>
                <div class="relative flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-14 h-14 bg-gradient-to-br from-emerald-400 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200">
                            <svg class="h-7 w-7 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-semibold text-gray-500 truncate uppercase tracking-wide">Ready</dt>
                            <dd class="text-3xl font-bold text-gray-900 mb-1">{{ ready_count }}</dd>
                            <dd class="text-xs text-gray-400">Ready for pickup</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-xl rounded-2xl border border-gray-100 card-hover group">
            <div class="p-6 relative">
                <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-purple-50 to-purple-100 rounded-bl-3xl opacity-50"></div>
                <div class="relative flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-14 h-14 bg-gradient-to-br from-purple-400 to-purple-500 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200">
                            <svg class="h-7 w-7 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-semibold text-gray-500 truncate uppercase tracking-wide">Completed</dt>
                            <dd class="text-3xl font-bold text-gray-900 mb-1">{{ claimed_count }}</dd>
                            <dd class="text-xs text-gray-400">Successfully processed</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <!-- Enhanced Filters and Search -->
    <div class="bg-white shadow-xl rounded-2xl border border-gray-100 mb-8">
        <div class="px-6 py-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900">Filter & Search Appointments</h3>
                <div class="w-8 h-8 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z" />
                    </svg>
                </div>
            </div>

            <!-- Search Bar -->
            <div class="mb-6">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </div>
                    <input type="text" id="appointment-search" placeholder="Search by student name, appointment ID, or service..."
                           class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500">
                </div>
            </div>

            <!-- Status Filters -->
            <div class="flex flex-wrap gap-3 mb-4">
                <a href="{% url 'staff_appointment_list' %}"
                   class="inline-flex items-center px-4 py-2 border-2 text-sm font-semibold rounded-xl transition-all duration-200
                          {% if not request.GET.status %}border-emerald-500 text-emerald-700 bg-emerald-50 shadow-lg{% else %}border-gray-200 text-gray-700 bg-white hover:bg-emerald-50 hover:border-emerald-300 hover:text-emerald-600{% endif %}">
                    <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    All Appointments
                </a>
                <a href="{% url 'staff_appointment_list' %}?status=pending"
                   class="inline-flex items-center px-4 py-2 border-2 text-sm font-semibold rounded-xl transition-all duration-200
                          {% if request.GET.status == 'pending' %}border-gold-500 text-gold-700 bg-gold-50 shadow-lg{% else %}border-gray-200 text-gray-700 bg-white hover:bg-gold-50 hover:border-gold-300 hover:text-gold-600{% endif %}">
                    <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Pending <span class="ml-1 px-2 py-0.5 bg-gold-200 text-gold-800 rounded-full text-xs">{{ pending_count }}</span>
                </a>
                <a href="{% url 'staff_appointment_list' %}?status=in_progress"
                   class="inline-flex items-center px-4 py-2 border-2 text-sm font-semibold rounded-xl transition-all duration-200
                          {% if request.GET.status == 'in_progress' %}border-blue-500 text-blue-700 bg-blue-50 shadow-lg{% else %}border-gray-200 text-gray-700 bg-white hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600{% endif %}">
                    <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                    </svg>
                    In Progress <span class="ml-1 px-2 py-0.5 bg-blue-200 text-blue-800 rounded-full text-xs">{{ in_progress_count }}</span>
                </a>
                <a href="{% url 'staff_appointment_list' %}?status=ready"
                   class="inline-flex items-center px-4 py-2 border-2 text-sm font-semibold rounded-xl transition-all duration-200
                          {% if request.GET.status == 'ready' %}border-emerald-500 text-emerald-700 bg-emerald-50 shadow-lg{% else %}border-gray-200 text-gray-700 bg-white hover:bg-emerald-50 hover:border-emerald-300 hover:text-emerald-600{% endif %}">
                    <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Ready <span class="ml-1 px-2 py-0.5 bg-emerald-200 text-emerald-800 rounded-full text-xs">{{ ready_count }}</span>
                </a>
                <a href="{% url 'staff_appointment_list' %}?status=claimed"
                   class="inline-flex items-center px-4 py-2 border-2 text-sm font-semibold rounded-xl transition-all duration-200
                          {% if request.GET.status == 'claimed' %}border-purple-500 text-purple-700 bg-purple-50 shadow-lg{% else %}border-gray-200 text-gray-700 bg-white hover:bg-purple-50 hover:border-purple-300 hover:text-purple-600{% endif %}">
                    <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Completed <span class="ml-1 px-2 py-0.5 bg-purple-200 text-purple-800 rounded-full text-xs">{{ claimed_count }}</span>
                </a>
            </div>

            <!-- Additional Filters -->
            <div class="flex flex-wrap gap-3 items-center">
                <select id="sort-by" class="rounded-xl border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500 text-sm">
                    <option value="created_at">Sort by Date Created</option>
                    <option value="student_name">Sort by Student Name</option>
                    <option value="service_name">Sort by Service</option>
                    <option value="status">Sort by Status</option>
                </select>

                <select id="date-range" class="rounded-xl border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500 text-sm">
                    <option value="all">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                </select>

                <button id="clear-filters" class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 transition-all duration-200">
                    <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Clear
                </button>
            </div>
        </div>
    </div>

    <!-- Bulk Actions Bar (Hidden by default) -->
    <div id="bulk-actions" class="bg-gradient-to-r from-emerald-600 to-emerald-700 rounded-2xl p-4 mb-6 text-white shadow-xl hidden">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span class="font-semibold"><span class="selected-count">0</span> appointment(s) selected</span>
            </div>
            <div class="flex items-center space-x-3">
                <button data-action="bulk-update" data-bulk-action="mark_in_progress" class="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 text-white rounded-xl hover:bg-opacity-30 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-all duration-200 font-medium">
                    <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                    </svg>
                    Mark In Progress
                </button>
                <button data-action="bulk-update" data-bulk-action="mark_ready" class="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 text-white rounded-xl hover:bg-opacity-30 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-all duration-200 font-medium">
                    <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Mark Ready
                </button>
                <button data-action="bulk-update" data-bulk-action="assign_staff" class="inline-flex items-center px-4 py-2 bg-white bg-opacity-20 text-white rounded-xl hover:bg-opacity-30 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-all duration-200 font-medium">
                    <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                    Assign Staff
                </button>
            </div>
        </div>
    </div>

    <!-- Appointments Table -->
    <div class="bg-white shadow-xl rounded-2xl border border-gray-100">
        <div class="px-6 py-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900">Appointments List</h3>
                <div class="text-sm text-gray-500">
                    Showing {{ appointments.count }} appointment{{ appointments.count|pluralize }}
                </div>
            </div>
            {% if appointments %}
            <div class="overflow-hidden rounded-xl border border-gray-200">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gradient-to-r from-emerald-50 to-emerald-100">
                        <tr>
                            <th class="px-6 py-4 text-left">
                                <input type="checkbox" id="select-all-appointments" class="rounded border-gray-300 text-emerald-600 shadow-sm focus:border-emerald-300 focus:ring focus:ring-emerald-200 focus:ring-opacity-50">
                            </th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-emerald-700 uppercase tracking-wider">Student</th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-emerald-700 uppercase tracking-wider">Service</th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-emerald-700 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-emerald-700 uppercase tracking-wider">Progress</th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-emerald-700 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-4 text-left text-xs font-semibold text-emerald-700 uppercase tracking-wider">Quick Actions</th>
                            <th class="relative px-6 py-4"><span class="sr-only">More Actions</span></th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for appointment in appointments %}
                        <tr class="hover:bg-gray-50 transition-colors duration-200 group" data-appointment-id="{{ appointment.appointment_id }}">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" data-bulk-select value="{{ appointment.appointment_id }}" class="rounded border-gray-300 text-emerald-600 shadow-sm focus:border-emerald-300 focus:ring focus:ring-emerald-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-12 w-12">
                                        <div class="h-12 w-12 rounded-xl bg-gradient-to-br from-emerald-100 to-emerald-200 flex items-center justify-center group-hover:scale-105 transition-transform duration-200">
                                            <span class="text-sm font-bold text-emerald-700">
                                                {{ appointment.student.first_name|first }}{{ appointment.student.last_name|first }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-semibold text-gray-900">{{ appointment.student.get_full_name }}</div>
                                        <div class="text-xs text-gray-500 font-mono">{{ appointment.appointment_id }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ appointment.service.name }}</div>
                                <div class="text-xs text-gray-500">{{ appointment.service.department.name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="status-badge inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full
                                    {% if appointment.status == 'pending' %}bg-gradient-to-r from-gold-100 to-gold-200 text-gold-800
                                    {% elif appointment.status == 'in_progress' %}bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800
                                    {% elif appointment.status == 'ready' %}bg-gradient-to-r from-emerald-100 to-emerald-200 text-emerald-800
                                    {% elif appointment.status == 'claimed' %}bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800
                                    {% endif %}">
                                    {% if appointment.status == 'pending' %}
                                        <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    {% elif appointment.status == 'in_progress' %}
                                        <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                                        </svg>
                                    {% elif appointment.status == 'ready' %}
                                        <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    {% elif appointment.status == 'claimed' %}
                                        <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    {% endif %}
                                    {{ appointment.get_status_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% with total=appointment.requirements.count %}
                                <div class="flex items-center">
                                    <div class="w-20 bg-gray-200 rounded-full h-2.5 mr-3">
                                        {% if total > 0 %}
                                        <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 h-2.5 rounded-full transition-all duration-300" style="width: {{ appointment.completion_percentage }}%"></div>
                                        {% else %}
                                        <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 h-2.5 rounded-full" style="width: 0%"></div>
                                        {% endif %}
                                    </div>
                                    <span class="text-xs font-medium text-gray-600">{{ appointment.completed_requirements_count }}/{{ total }}</span>
                                </div>
                                {% endwith %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ appointment.created_at|date:"M d, Y" }}</div>
                                <div class="text-xs text-gray-500">{{ appointment.created_at|date:"g:i A" }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center space-x-2">
                                    <!-- Quick Status Update Buttons -->
                                    {% if appointment.status == 'pending' %}
                                    <button data-action="update-status" data-appointment-id="{{ appointment.appointment_id }}" data-new-status="in_progress" data-current-status="{{ appointment.status }}"
                                            class="quick-action-btn inline-flex items-center px-2 py-1 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-all duration-200 text-xs font-medium">
                                        <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        Start
                                    </button>
                                    {% elif appointment.status == 'in_progress' %}
                                    <button data-action="update-status" data-appointment-id="{{ appointment.appointment_id }}" data-new-status="ready" data-current-status="{{ appointment.status }}"
                                            class="quick-action-btn inline-flex items-center px-2 py-1 bg-emerald-100 text-emerald-700 rounded-lg hover:bg-emerald-200 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-1 transition-all duration-200 text-xs font-medium">
                                        <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        Ready
                                    </button>
                                    {% elif appointment.status == 'ready' %}
                                    <span class="inline-flex items-center px-2 py-1 bg-emerald-100 text-emerald-700 rounded-lg text-xs font-medium">
                                        <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        Ready
                                    </span>
                                    {% endif %}

                                    <!-- Quick Note Button -->
                                    <button data-action="add-note" data-appointment-id="{{ appointment.appointment_id }}"
                                            class="quick-action-btn inline-flex items-center px-2 py-1 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1 transition-all duration-200 text-xs font-medium">
                                        <svg class="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                        </svg>
                                    </button>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right">
                                <div class="flex items-center justify-end space-x-2">
                                    <a href="{% url 'staff_appointment_detail' appointment.appointment_id %}"
                                       class="inline-flex items-center px-3 py-2 border border-emerald-300 text-sm font-medium rounded-xl text-emerald-700 bg-emerald-50 hover:bg-emerald-100 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 transition-all duration-200">
                                        <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                        </svg>
                                        View
                                    </a>

                                    <!-- Dropdown Menu -->
                                    <div class="relative inline-block text-left">
                                        <button type="button" class="inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 transition-all duration-200" onclick="toggleDropdown('dropdown-{{ appointment.appointment_id }}')">
                                            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                                            </svg>
                                        </button>

                                        <div id="dropdown-{{ appointment.appointment_id }}" class="hidden origin-top-right absolute right-0 mt-2 w-48 rounded-xl shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                                            <div class="py-1">
                                                <button data-action="assign-staff" data-appointment-id="{{ appointment.appointment_id }}" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                                                    <svg class="w-4 h-4 mr-2 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                                    </svg>
                                                    Assign Staff
                                                </button>
                                                <button data-action="view-history" data-appointment-id="{{ appointment.appointment_id }}" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                                                    <svg class="w-4 h-4 mr-2 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                    View History
                                                </button>
                                                <a href="{% url 'generate_qr_code' appointment.appointment_id %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900">
                                                    <svg class="w-4 h-4 mr-2 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.75 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 013.75 9.375v-4.5zM3.75 14.625c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5a1.125 1.125 0 01-1.125-1.125v-4.5zM13.5 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 0113.5 9.375v-4.5z" />
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 6.75h.75v.75h-.75v-.75zM6.75 16.5h.75v.75h-.75v-.75zM16.5 6.75h.75v.75h-.75v-.75zM13.5 13.5h4.5v4.5h-4.5v-4.5z" />
                                                    </svg>
                                                    Generate QR Code
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Previous</a>
                    {% endif %}
                    {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Next</a>
                    {% endif %}
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing <span class="font-medium">{{ page_obj.start_index }}</span> to <span class="font-medium">{{ page_obj.end_index }}</span> of <span class="font-medium">{{ page_obj.paginator.count }}</span> results
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                            {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">Previous</a>
                            {% endif %}
                            {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">Next</a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </div>
            {% endif %}
            
            {% else %}
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No appointments</h3>
                <p class="mt-1 text-sm text-gray-500">
                    {% if current_status != 'all' %}
                        No appointments found with status "{{ current_status }}".
                    {% else %}
                        No appointments found for your department.
                    {% endif %}
                </p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Include Modal Components -->
{% include 'components/modals.html' %}

<!-- Auto-save indicator -->
<div id="auto-save-indicator" class="fixed bottom-4 right-4 px-3 py-1 bg-white rounded-lg shadow-lg text-sm font-medium opacity-0 transition-opacity duration-200"></div>

<script>
// Dropdown toggle function
function toggleDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');

    // Close all other dropdowns
    allDropdowns.forEach(dd => {
        if (dd.id !== dropdownId) {
            dd.classList.add('hidden');
        }
    });

    // Toggle current dropdown
    dropdown.classList.toggle('hidden');
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('[onclick^="toggleDropdown"]') && !event.target.closest('[id^="dropdown-"]')) {
        document.querySelectorAll('[id^="dropdown-"]').forEach(dd => {
            dd.classList.add('hidden');
        });
    }
});

// Search functionality
document.getElementById('appointment-search').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const rows = document.querySelectorAll('tbody tr[data-appointment-id]');

    rows.forEach(row => {
        const studentName = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
        const appointmentId = row.querySelector('td:nth-child(2) .font-mono').textContent.toLowerCase();
        const serviceName = row.querySelector('td:nth-child(3)').textContent.toLowerCase();

        if (studentName.includes(searchTerm) || appointmentId.includes(searchTerm) || serviceName.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});

// Sort functionality
document.getElementById('sort-by').addEventListener('change', function(e) {
    const sortBy = e.target.value;
    const tbody = document.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr[data-appointment-id]'));

    rows.sort((a, b) => {
        let aValue, bValue;

        switch(sortBy) {
            case 'student_name':
                aValue = a.querySelector('td:nth-child(2) .font-semibold').textContent;
                bValue = b.querySelector('td:nth-child(2) .font-semibold').textContent;
                break;
            case 'service_name':
                aValue = a.querySelector('td:nth-child(3) .font-medium').textContent;
                bValue = b.querySelector('td:nth-child(3) .font-medium').textContent;
                break;
            case 'status':
                aValue = a.querySelector('.status-badge').textContent;
                bValue = b.querySelector('.status-badge').textContent;
                break;
            case 'created_at':
            default:
                aValue = a.querySelector('td:nth-child(6) .text-sm').textContent;
                bValue = b.querySelector('td:nth-child(6) .text-sm').textContent;
                break;
        }

        return aValue.localeCompare(bValue);
    });

    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
});

// Clear filters
document.getElementById('clear-filters').addEventListener('click', function() {
    document.getElementById('appointment-search').value = '';
    document.getElementById('sort-by').value = 'created_at';
    document.getElementById('date-range').value = 'all';

    // Show all rows
    document.querySelectorAll('tbody tr[data-appointment-id]').forEach(row => {
        row.style.display = '';
    });

    // Clear bulk selections
    document.querySelectorAll('[data-bulk-select]').forEach(cb => cb.checked = false);
    document.getElementById('select-all-appointments').checked = false;
    document.getElementById('bulk-actions').classList.add('hidden');
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + F for search
    if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
        e.preventDefault();
        document.getElementById('appointment-search').focus();
    }

    // Escape to clear search
    if (e.key === 'Escape' && document.activeElement === document.getElementById('appointment-search')) {
        document.getElementById('appointment-search').value = '';
        document.getElementById('appointment-search').dispatchEvent(new Event('input'));
    }
});
</script>
{% endblock %}
