#!/usr/bin/env python
"""
Test script for Staff Interface functionality
Tests the office staff appointment management features
"""

import os
import sys
import django
from django.test import Client
from django.contrib.auth import get_user_model
from django.urls import reverse

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UnifiedServices.settings')
django.setup()

from appointments.models import UserProfile, Department, Service, Appointment, AppointmentRequirement

def test_staff_interface():
    """Test staff interface functionality"""
    print("🧪 Testing Staff Interface Functionality")
    print("=" * 50)
    
    client = Client()
    User = get_user_model()
    
    try:
        # Get test users
        staff_user = User.objects.get(username='registrar1')
        student_user = User.objects.get(username='student1')
        
        print(f"✅ Found staff user: {staff_user.get_full_name()}")
        print(f"✅ Found student user: {student_user.get_full_name()}")
        
        # Test staff login
        login_success = client.login(username='registrar1', password='password123')
        if login_success:
            print("✅ Staff login successful")
        else:
            print("❌ Staff login failed")
            return False
        
        # Test staff dashboard access
        response = client.get(reverse('staff_dashboard'))
        if response.status_code == 200:
            print("✅ Staff dashboard accessible")
        else:
            print(f"❌ Staff dashboard failed: {response.status_code}")
            return False
        
        # Test staff appointment list
        response = client.get(reverse('staff_appointment_list'))
        if response.status_code == 200:
            print("✅ Staff appointment list accessible")
            if response.context and 'appointments' in response.context:
                print(f"   📊 Found {len(response.context['appointments'])} appointments")
            else:
                print("   📊 Appointment list rendered successfully")
        else:
            print(f"❌ Staff appointment list failed: {response.status_code}")
            return False
        
        # Test filtered appointment list
        response = client.get(reverse('staff_appointment_list') + '?status=pending')
        if response.status_code == 200:
            print("✅ Filtered appointment list (pending) accessible")
        else:
            print(f"❌ Filtered appointment list failed: {response.status_code}")
            return False
        
        # Get a test appointment
        staff_department = staff_user.userprofile.department
        appointments = Appointment.objects.filter(service__department=staff_department)
        
        if appointments.exists():
            test_appointment = appointments.first()
            print(f"✅ Found test appointment: {test_appointment.appointment_id}")
            
            # Test staff appointment detail view
            response = client.get(reverse('staff_appointment_detail', kwargs={'pk': test_appointment.appointment_id}))
            if response.status_code == 200:
                print("✅ Staff appointment detail accessible")
                if response.context:
                    print(f"   📋 Requirements: {response.context.get('total_requirements', 'N/A')}")
                    print(f"   ✅ Completed: {response.context.get('completed_requirements', 'N/A')}")
                else:
                    print("   📋 Detail view rendered successfully")
            else:
                print(f"❌ Staff appointment detail failed: {response.status_code}")
                return False
            
            # Test requirement toggle (HTMX endpoint)
            requirements = test_appointment.requirements.all()
            if requirements.exists():
                test_requirement = requirements.first()
                original_status = test_requirement.is_completed
                
                response = client.post(
                    reverse('toggle_requirement_completion', kwargs={
                        'appointment_id': test_appointment.appointment_id,
                        'requirement_id': test_requirement.id
                    })
                )
                
                if response.status_code == 200:
                    print("✅ Requirement toggle endpoint accessible")
                    
                    # Check if status actually changed
                    test_requirement.refresh_from_db()
                    if test_requirement.is_completed != original_status:
                        print("✅ Requirement status successfully toggled")
                        
                        # Toggle back to original state
                        client.post(
                            reverse('toggle_requirement_completion', kwargs={
                                'appointment_id': test_appointment.appointment_id,
                                'requirement_id': test_requirement.id
                            })
                        )
                        test_requirement.refresh_from_db()
                        print("✅ Requirement status restored")
                    else:
                        print("⚠️  Requirement status not changed (may be expected)")
                else:
                    print(f"❌ Requirement toggle failed: {response.status_code}")
                    return False
            
            # Test appointment status update
            original_status = test_appointment.status
            new_status = 'in_progress' if original_status != 'in_progress' else 'pending'
            
            response = client.post(
                reverse('update_appointment_status', kwargs={'appointment_id': test_appointment.appointment_id}),
                data='{"status": "' + new_status + '"}',
                content_type='application/json'
            )
            
            if response.status_code == 200:
                print("✅ Appointment status update endpoint accessible")
                
                # Check if status actually changed
                test_appointment.refresh_from_db()
                if test_appointment.status == new_status:
                    print(f"✅ Appointment status successfully updated to {new_status}")
                    
                    # Restore original status
                    client.post(
                        reverse('update_appointment_status', kwargs={'appointment_id': test_appointment.appointment_id}),
                        data='{"status": "' + original_status + '"}',
                        content_type='application/json'
                    )
                    test_appointment.refresh_from_db()
                    print("✅ Appointment status restored")
                else:
                    print("⚠️  Appointment status not changed")
            else:
                print(f"❌ Appointment status update failed: {response.status_code}")
                return False
            
            # Test note addition
            if requirements.exists():
                test_requirement = requirements.first()
                test_note = "Test note from staff interface test"
                
                response = client.post(
                    reverse('add_requirement_note', kwargs={
                        'appointment_id': test_appointment.appointment_id,
                        'requirement_id': test_requirement.id
                    }),
                    data='{"note": "' + test_note + '"}',
                    content_type='application/json'
                )
                
                if response.status_code == 200:
                    print("✅ Requirement note addition endpoint accessible")
                    
                    # Check if note was added
                    test_requirement.refresh_from_db()
                    if test_requirement.notes == test_note:
                        print("✅ Requirement note successfully added")
                        
                        # Clear the note
                        test_requirement.notes = ""
                        test_requirement.save()
                        print("✅ Test note cleared")
                    else:
                        print("⚠️  Requirement note not saved")
                else:
                    print(f"❌ Requirement note addition failed: {response.status_code}")
                    return False
        
        else:
            print("⚠️  No appointments found for testing detailed functionality")
        
        # Test access control - try accessing with student user
        client.logout()
        student_login = client.login(username='student1', password='password123')
        
        if student_login:
            print("✅ Student login successful")
            
            # Student should not be able to access staff views
            response = client.get(reverse('staff_appointment_list'))
            if response.status_code == 403 or response.status_code == 302:
                print("✅ Access control working - student cannot access staff views")
            else:
                print(f"❌ Access control failed - student can access staff views: {response.status_code}")
                return False
        
        print("\n🎉 All Staff Interface Tests Passed!")
        print("=" * 50)
        print("✅ Staff Dashboard Access")
        print("✅ Staff Appointment List")
        print("✅ Staff Appointment Detail")
        print("✅ Requirement Management (Toggle)")
        print("✅ Appointment Status Updates")
        print("✅ Requirement Notes")
        print("✅ Access Control")
        print("✅ HTMX Endpoints")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_staff_interface()
    sys.exit(0 if success else 1)
