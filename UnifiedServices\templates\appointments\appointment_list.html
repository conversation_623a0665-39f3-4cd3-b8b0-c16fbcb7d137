{% extends 'base.html' %}

{% block title %}My Appointments - JHCSC Unified Student Services{% endblock %}

{% block page_title %}My Appointments{% endblock %}

{% block extra_head %}
<!-- Custom Tailwind Config for Emerald & Gold Theme -->
<script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    emerald: {
                        50: '#ecfdf5',
                        100: '#d1fae5',
                        200: '#a7f3d0',
                        300: '#6ee7b7',
                        400: '#34d399',
                        500: '#10b981',
                        600: '#059669',
                        700: '#047857',
                        800: '#065f46',
                        900: '#064e3b',
                        950: '#022c22'
                    },
                    gold: {
                        50: '#fffbeb',
                        100: '#fef3c7',
                        200: '#fde68a',
                        300: '#fcd34d',
                        400: '#fbbf24',
                        500: '#f59e0b',
                        600: '#d97706',
                        700: '#b45309',
                        800: '#92400e',
                        900: '#78350f'
                    }
                },
                animation: {
                    'fade-in': 'fadeIn 0.6s ease-out',
                    'slide-up': 'slideUp 0.6s ease-out',
                    'card-hover': 'cardHover 0.3s ease',
                    'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                },
                keyframes: {
                    fadeIn: {
                        '0%': { opacity: '0' },
                        '100%': { opacity: '1' }
                    },
                    slideUp: {
                        '0%': { opacity: '0', transform: 'translateY(20px)' },
                        '100%': { opacity: '1', transform: 'translateY(0)' }
                    },
                    cardHover: {
                        '0%': { transform: 'translateY(0)' },
                        '100%': { transform: 'translateY(-4px)' }
                    }
                }
            }
        }
    }
</script>

<style>
    [x-cloak] { display: none !important; }

    .card-hover {
        transition: all 0.3s ease;
    }

    .card-hover:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .status-badge {
        transition: all 0.2s ease;
    }

    .status-badge:hover {
        transform: scale(1.05);
    }
</style>
{% endblock %}

{% block mobile_nav %}
{% include 'navigation/student_nav.html' %}
{% endblock %}

{% block desktop_nav %}
{% include 'navigation/student_nav.html' %}
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto animate-fade-in">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-emerald-600 to-emerald-700 rounded-2xl p-6 mb-8 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold mb-2">My Appointments</h1>
                <p class="text-emerald-100">Track the status of your service requests and appointments</p>
            </div>
            <div class="hidden md:block">
                <div class="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-gold-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8 animate-slide-up">
        <div class="bg-white overflow-hidden shadow-lg rounded-xl border border-gray-100 card-hover">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-br from-gold-400 to-gold-500 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Pending</dt>
                            <dd class="text-2xl font-bold text-gray-900">{{ appointments|length }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-xl border border-gray-100 card-hover">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-br from-emerald-400 to-emerald-500 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Ready</dt>
                            <dd class="text-2xl font-bold text-gray-900">0</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-xl border border-gray-100 card-hover">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-500 rounded-xl flex items-center justify-center shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total</dt>
                            <dd class="text-2xl font-bold text-gray-900">{{ appointments|length }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-xl border border-gray-100 card-hover">
            <div class="p-6 text-center">
                <a href="{% url 'appointment_create' %}"
                   class="inline-flex items-center justify-center w-full px-4 py-3 bg-gradient-to-r from-emerald-600 to-emerald-700 text-white font-semibold rounded-xl hover:from-emerald-700 hover:to-emerald-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-all duration-200 shadow-lg hover:shadow-xl card-hover">
                    <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.5v15m7.5-7.5h-15" />
                    </svg>
                    New Request
                </a>
            </div>
        </div>
    </div>

    {% if appointments %}
    <!-- Appointments list -->
    <div class="space-y-4">
        {% for appointment in appointments %}
        <div class="bg-white shadow-xl rounded-2xl border border-gray-100 card-hover overflow-hidden">
            <a href="{% url 'appointment_detail' appointment.pk %}"
               class="block p-6 hover:bg-gradient-to-r hover:from-emerald-50 hover:to-gold-50 transition-all duration-300"
               up-target="main"
               up-history="true">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0">
                            <div class="h-16 w-16 rounded-2xl bg-gradient-to-br from-emerald-500 to-emerald-600 flex items-center justify-center shadow-lg">
                                <span class="text-lg font-bold text-white">{{ appointment.service.department.code }}</span>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center space-x-3 mb-2">
                                <h3 class="text-xl font-bold text-gray-900 truncate">
                                    {{ appointment.service.name }}
                                </h3>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700">
                                    #{{ appointment.appointment_id }}
                                </span>
                            </div>
                            <p class="text-lg text-gray-600 mb-2">{{ appointment.service.department.name }}</p>
                            <div class="flex items-center text-sm text-gray-500 space-x-4">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                    {{ appointment.created_at|date:"M d, Y" }}
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    {{ appointment.created_at|date:"g:i A" }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="status-badge inline-flex items-center px-4 py-2 rounded-xl text-sm font-semibold shadow-sm
                            {% if appointment.status == 'pending' %}bg-gradient-to-r from-gold-100 to-gold-200 text-gold-800
                            {% elif appointment.status == 'in_progress' %}bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800
                            {% elif appointment.status == 'ready' %}bg-gradient-to-r from-emerald-100 to-emerald-200 text-emerald-800
                            {% elif appointment.status == 'claimed' %}bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800
                            {% else %}bg-gradient-to-r from-red-100 to-red-200 text-red-800{% endif %}">
                            <div class="w-2 h-2 rounded-full mr-2
                                {% if appointment.status == 'pending' %}bg-gold-500 animate-pulse-slow
                                {% elif appointment.status == 'in_progress' %}bg-blue-500 animate-pulse-slow
                                {% elif appointment.status == 'ready' %}bg-emerald-500 animate-pulse-slow
                                {% elif appointment.status == 'claimed' %}bg-gray-500
                                {% else %}bg-red-500 animate-pulse-slow{% endif %}"></div>
                            {{ appointment.get_status_display }}
                        </span>
                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-emerald-100 text-emerald-600 group-hover:bg-emerald-200 transition-colors duration-200">
                            <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </div>
                        </div>
                </div>
                {% if appointment.notes %}
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <div class="flex items-start space-x-2">
                        <svg class="w-4 h-4 text-gray-400 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                        </svg>
                        <p class="text-sm text-gray-600 italic">{{ appointment.notes|truncatewords:20 }}</p>
                    </div>
                </div>
                {% endif %}
            </a>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <nav class="bg-white shadow-lg rounded-2xl px-6 py-4 flex items-center justify-between mt-8 border border-gray-100" aria-label="Pagination">
        <div class="hidden sm:block">
            <p class="text-sm text-gray-700">
                Showing
                <span class="font-semibold text-emerald-600">{{ page_obj.start_index }}</span>
                to
                <span class="font-semibold text-emerald-600">{{ page_obj.end_index }}</span>
                of
                <span class="font-semibold text-emerald-600">{{ paginator.count }}</span>
                results
            </p>
        </div>
        <div class="flex-1 flex justify-between sm:justify-end space-x-3">
            {% if page_obj.has_previous %}
            <a href="?page={{ page_obj.previous_page_number }}"
               class="relative inline-flex items-center px-6 py-3 border-2 border-emerald-200 text-sm font-semibold rounded-xl text-emerald-700 bg-white hover:bg-emerald-50 hover:border-emerald-300 transition-all duration-200 card-hover">
                <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
                Previous
            </a>
            {% endif %}
            {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}"
               class="relative inline-flex items-center px-6 py-3 border border-transparent text-sm font-semibold rounded-xl text-white bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 transition-all duration-200 shadow-lg hover:shadow-xl card-hover">
                Next
                <svg class="w-4 h-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </a>
            {% endif %}
        </div>
    </nav>
    {% endif %}

    {% else %}
    <!-- Empty state -->
    <div class="bg-white shadow-xl rounded-2xl border border-gray-100 text-center py-16 px-8">
        <div class="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl flex items-center justify-center">
            <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
        </div>
        <h3 class="text-2xl font-bold text-gray-900 mb-3">No appointments yet</h3>
        <p class="text-lg text-gray-600 mb-8 max-w-md mx-auto">Ready to get started? Create your first appointment to access our student services.</p>
        <div class="space-y-4">
            <a href="{% url 'appointment_create' %}"
               class="inline-flex items-center px-8 py-4 border border-transparent text-lg font-semibold rounded-xl text-white bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-all duration-200 shadow-lg hover:shadow-xl card-hover">
                <svg class="w-6 h-6 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.5v15m7.5-7.5h-15" />
                </svg>
                Create Your First Appointment
            </a>
            <div class="text-sm text-gray-500">
                <a href="{% url 'service_list' %}" class="text-emerald-600 hover:text-emerald-700 font-medium transition-colors duration-200">
                    Browse available services →
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
