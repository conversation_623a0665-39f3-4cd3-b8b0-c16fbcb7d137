#!/usr/bin/env python
"""
Test runner script for the Unified Student Services system
"""

import os
import sys
import django
from django.conf import settings
from django.test.utils import get_runner
from django.core.management import execute_from_command_line


def run_tests():
    """Run all tests for the appointments app"""
    
    # Set up Django environment
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UnifiedServices.settings')
    django.setup()
    
    # Import test modules to ensure they're discovered
    from appointments.tests import test_models, test_views, test_security, test_utils
    
    print("=" * 70)
    print("UNIFIED STUDENT SERVICES - TEST SUITE")
    print("=" * 70)
    print()
    
    # Run specific test categories
    test_categories = [
        ('Model Tests', 'appointments.tests.test_models'),
        ('View Tests', 'appointments.tests.test_views'),
        ('Security Tests', 'appointments.tests.test_security'),
        ('Utility Tests', 'appointments.tests.test_utils'),
    ]
    
    total_tests = 0
    total_failures = 0
    
    for category_name, test_module in test_categories:
        print(f"\n{'-' * 50}")
        print(f"Running {category_name}")
        print(f"{'-' * 50}")
        
        # Run tests for this category
        try:
            from django.test.runner import DiscoverRunner
            test_runner = DiscoverRunner(verbosity=2, interactive=False)
            
            # Run the specific test module
            result = test_runner.run_tests([test_module])
            
            if result == 0:
                print(f"✅ {category_name} - All tests passed!")
            else:
                print(f"❌ {category_name} - {result} test(s) failed")
                total_failures += result
                
        except Exception as e:
            print(f"❌ {category_name} - Error running tests: {e}")
            total_failures += 1
    
    print("\n" + "=" * 70)
    print("TEST SUMMARY")
    print("=" * 70)
    
    if total_failures == 0:
        print("🎉 ALL TESTS PASSED! 🎉")
        print("The Unified Student Services system is ready for deployment.")
    else:
        print(f"❌ {total_failures} test category(ies) had failures")
        print("Please review the test output above for details.")
    
    print("=" * 70)
    
    return total_failures


def run_coverage_tests():
    """Run tests with coverage reporting"""
    
    try:
        import coverage
        
        print("Running tests with coverage analysis...")
        
        # Start coverage
        cov = coverage.Coverage()
        cov.start()
        
        # Run tests
        failures = run_tests()
        
        # Stop coverage and generate report
        cov.stop()
        cov.save()
        
        print("\n" + "=" * 70)
        print("COVERAGE REPORT")
        print("=" * 70)
        
        # Generate coverage report
        cov.report(show_missing=True)
        
        # Generate HTML coverage report
        cov.html_report(directory='htmlcov')
        print("\nHTML coverage report generated in 'htmlcov' directory")
        
        return failures
        
    except ImportError:
        print("Coverage package not installed. Running tests without coverage...")
        return run_tests()


def run_specific_test(test_path):
    """Run a specific test"""
    
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'UnifiedServices.settings')
    django.setup()
    
    from django.test.runner import DiscoverRunner
    test_runner = DiscoverRunner(verbosity=2, interactive=False)
    
    result = test_runner.run_tests([test_path])
    return result


def main():
    """Main test runner function"""
    
    if len(sys.argv) > 1:
        if sys.argv[1] == '--coverage':
            return run_coverage_tests()
        elif sys.argv[1] == '--help':
            print("Usage:")
            print("  python run_tests.py                 - Run all tests")
            print("  python run_tests.py --coverage      - Run tests with coverage")
            print("  python run_tests.py <test_path>     - Run specific test")
            print()
            print("Examples:")
            print("  python run_tests.py appointments.tests.test_models")
            print("  python run_tests.py appointments.tests.test_models.UserProfileModelTest")
            print("  python run_tests.py appointments.tests.test_models.UserProfileModelTest.test_user_profile_creation")
            return 0
        else:
            # Run specific test
            test_path = sys.argv[1]
            return run_specific_test(test_path)
    else:
        return run_tests()


if __name__ == '__main__':
    sys.exit(main())
