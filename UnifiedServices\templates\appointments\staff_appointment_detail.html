{% extends 'base.html' %}
{% load static %}

{% block title %}Appointment {{ appointment.appointment_id }} - JHCSC Unified Student Services{% endblock %}

{% block page_title %}Appointment Management{% endblock %}

{% block extra_head %}
<!-- Custom Tailwind Config for Emerald & Gold Theme -->
<script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    emerald: {
                        50: '#ecfdf5',
                        100: '#d1fae5',
                        200: '#a7f3d0',
                        300: '#6ee7b7',
                        400: '#34d399',
                        500: '#10b981',
                        600: '#059669',
                        700: '#047857',
                        800: '#065f46',
                        900: '#064e3b',
                        950: '#022c22'
                    },
                    gold: {
                        50: '#fffbeb',
                        100: '#fef3c7',
                        200: '#fde68a',
                        300: '#fcd34d',
                        400: '#fbbf24',
                        500: '#f59e0b',
                        600: '#d97706',
                        700: '#b45309',
                        800: '#92400e',
                        900: '#78350f'
                    }
                },
                animation: {
                    'fade-in': 'fadeIn 0.6s ease-out',
                    'slide-up': 'slideUp 0.6s ease-out',
                },
                keyframes: {
                    fadeIn: {
                        '0%': { opacity: '0' },
                        '100%': { opacity: '1' }
                    },
                    slideUp: {
                        '0%': { opacity: '0', transform: 'translateY(20px)' },
                        '100%': { opacity: '1', transform: 'translateY(0)' }
                    }
                }
            }
        }
    }
</script>

<style>
    .card-hover {
        transition: all 0.3s ease;
    }
    .card-hover:hover {
        transform: translateY(-2px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    .hero-gradient {
        background: linear-gradient(135deg, #064e3b 0%, #047857 50%, #059669 100%);
    }
</style>
{% endblock %}

{% block mobile_nav %}
{% include 'navigation/staff_nav.html' %}
{% endblock %}

{% block desktop_nav %}
{% include 'navigation/staff_nav.html' %}
{% endblock %}

{% block content %}
<div class="animate-fade-in">
    <!-- Header Section -->
    <div class="hero-gradient rounded-2xl p-8 mb-8 text-white shadow-2xl relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <svg class="w-full h-full" viewBox="0 0 100 100" fill="none">
                <defs>
                    <pattern id="detail-grid" width="10" height="10" patternUnits="userSpaceOnUse">
                        <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" stroke-width="0.5"/>
                    </pattern>
                </defs>
                <rect width="100" height="100" fill="url(#detail-grid)" />
            </svg>
        </div>

        <div class="relative">
            <!-- Breadcrumb -->
            <nav class="flex mb-6" aria-label="Breadcrumb">
                <ol role="list" class="flex items-center space-x-4">
                    <li>
                        <div>
                            <a href="{% url 'staff_appointment_list' %}" class="text-emerald-200 hover:text-white transition-colors duration-200">
                                <svg class="flex-shrink-0 h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                                </svg>
                                <span class="sr-only">Back</span>
                            </a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="flex-shrink-0 h-5 w-5 text-emerald-300" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                            </svg>
                            <a href="{% url 'staff_appointment_list' %}" class="ml-4 text-sm font-medium text-emerald-200 hover:text-white transition-colors duration-200">Appointments</a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="flex-shrink-0 h-5 w-5 text-emerald-300" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                            </svg>
                            <span class="ml-4 text-sm font-medium text-emerald-100">{{ appointment.appointment_id }}</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold mb-1">Appointment {{ appointment.appointment_id }}</h1>
                            <p class="text-emerald-100 text-lg font-medium">{{ appointment.service.name }}</p>
                        </div>
                    </div>
                    <p class="text-emerald-200 text-sm">{{ appointment.service.department.name }} • Student: {{ appointment.student.get_full_name }}</p>
                </div>
                <div class="hidden lg:flex items-center space-x-4">
                    <span class="inline-flex items-center px-4 py-2 rounded-xl text-sm font-semibold bg-white bg-opacity-20 text-white backdrop-blur-sm
                        {% if appointment.status == 'pending' %}border-2 border-gold-300
                        {% elif appointment.status == 'in_progress' %}border-2 border-blue-300
                        {% elif appointment.status == 'ready' %}border-2 border-emerald-300
                        {% elif appointment.status == 'claimed' %}border-2 border-purple-300
                        {% endif %}">
                        {% if appointment.status == 'pending' %}
                            <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        {% elif appointment.status == 'in_progress' %}
                            <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                            </svg>
                        {% elif appointment.status == 'ready' %}
                            <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        {% elif appointment.status == 'claimed' %}
                            <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        {% endif %}
                        {{ appointment.get_status_display }}
                    </span>
                    <!-- Quick Action Buttons -->
                    <div class="flex items-center space-x-3">
                        <!-- Status Update Buttons -->
                        {% if appointment.status == 'pending' %}
                        <button data-action="quick-status-update" data-appointment-id="{{ appointment.appointment_id }}" data-new-status="in_progress" data-current-status="{{ appointment.status }}"
                                class="inline-flex items-center px-4 py-2 border-2 border-blue-300 rounded-xl shadow-lg text-sm font-semibold text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 transition-all duration-200">
                            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Start Processing
                        </button>
                        {% elif appointment.status == 'in_progress' %}
                        <button data-action="quick-status-update" data-appointment-id="{{ appointment.appointment_id }}" data-new-status="ready" data-current-status="{{ appointment.status }}"
                                class="inline-flex items-center px-4 py-2 border-2 border-emerald-300 rounded-xl shadow-lg text-sm font-semibold text-white bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:ring-opacity-50 transition-all duration-200">
                            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Mark Ready
                        </button>
                        {% endif %}

                        <!-- Staff Assignment Button -->
                        <button data-action="assign-staff" data-appointment-id="{{ appointment.appointment_id }}"
                                class="inline-flex items-center px-4 py-2 border-2 border-purple-300 rounded-xl shadow-lg text-sm font-semibold text-white bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-opacity-50 transition-all duration-200">
                            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                            </svg>
                            Assign Staff
                        </button>

                        <!-- Add Note Button -->
                        <button data-action="add-note" data-appointment-id="{{ appointment.appointment_id }}"
                                class="inline-flex items-center px-4 py-2 border-2 border-gray-300 rounded-xl shadow-lg text-sm font-semibold text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 transition-all duration-200">
                            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                            Add Note
                        </button>

                        {% if can_generate_qr %}
                        <a href="{% url 'generate_qr_code' appointment.appointment_id %}"
                           class="inline-flex items-center px-6 py-3 border-2 border-gold-300 rounded-xl shadow-lg text-sm font-semibold text-white bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 focus:outline-none focus:ring-2 focus:ring-gold-400 focus:ring-opacity-50 transition-all duration-200">
                            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.75 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 013.75 9.375v-4.5zM3.75 14.625c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5a1.125 1.125 0 01-1.125-1.125v-4.5zM13.5 4.875c0-.621.504-1.125 1.125-1.125h4.5c.621 0 1.125.504 1.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125h-4.5A1.125 1.125 0 0113.5 9.375v-4.5z" />
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 6.75h.75v.75h-.75v-.75zM6.75 16.5h.75v.75h-.75v-.75zM16.5 6.75h.75v.75h-.75v-.75zM13.5 13.5h4.5v4.5h-4.5v-4.5z" />
                            </svg>
                            Generate QR Code
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Student Information -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Student Information</h3>
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Name</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ appointment.student.get_full_name }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Email</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ appointment.student.email }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Service Requested</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ appointment.service.name }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Processing Time</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ appointment.service.processing_time }}</dd>
                        </div>
                        {% if appointment.notes %}
                        <div class="sm:col-span-2">
                            <dt class="text-sm font-medium text-gray-500">Student Notes</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ appointment.notes }}</dd>
                        </div>
                        {% endif %}
                    </dl>
                </div>
            </div>

            <!-- Requirements Management -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Requirements</h3>
                        <div class="text-sm text-gray-500">
                            {{ completed_requirements }}/{{ total_requirements }} completed
                        </div>
                    </div>
                    
                    <div class="space-y-4" id="requirements-list">
                        {% for req in requirements %}
                        <div class="border border-gray-200 rounded-lg p-4" id="requirement-{{ req.id }}">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center">
                                        <h4 class="text-sm font-medium text-gray-900">{{ req.requirement.name }}</h4>
                                        {% if req.requirement.is_required %}
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Required
                                        </span>
                                        {% endif %}
                                    </div>
                                    {% if req.requirement.description %}
                                    <p class="mt-1 text-sm text-gray-500">{{ req.requirement.description }}</p>
                                    {% endif %}
                                    
                                    <!-- File Upload Section -->
                                    {% if req.requirement.requires_upload %}
                                    <div class="mt-3">
                                        {% if req.uploaded_file %}
                                        <div class="flex items-center text-sm text-green-600">
                                            <svg class="flex-shrink-0 mr-1.5 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z" clip-rule="evenodd" />
                                            </svg>
                                            <a href="{{ req.uploaded_file.url }}" target="_blank" class="hover:underline">
                                                {{ req.uploaded_file.name|slice:"10:" }}
                                            </a>
                                        </div>
                                        {% else %}
                                        <div class="text-sm text-gray-500">
                                            <svg class="inline mr-1.5 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                            </svg>
                                            No file uploaded
                                        </div>
                                        {% endif %}
                                    </div>
                                    {% endif %}
                                    
                                    <!-- Staff Notes -->
                                    {% if req.notes %}
                                    <div class="mt-3 p-3 bg-yellow-50 rounded-md">
                                        <div class="flex">
                                            <svg class="flex-shrink-0 h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                            </svg>
                                            <div class="ml-3">
                                                <p class="text-sm text-yellow-700">{{ req.notes }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}
                                    
                                    <!-- Completion Status -->
                                    {% if req.is_completed %}
                                    <div class="mt-3 text-sm text-green-600">
                                        <svg class="inline mr-1.5 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        Completed by {{ req.completed_by.get_full_name }} on {{ req.completed_at|date:"M d, Y H:i" }}
                                    </div>
                                    {% endif %}
                                </div>
                                
                                <!-- Action Buttons -->
                                <div class="ml-4 flex-shrink-0 flex space-x-2">
                                    <!-- Toggle Completion Button -->
                                    <button type="button"
                                            hx-post="{% url 'toggle_requirement_completion' appointment.appointment_id req.id %}"
                                            hx-target="#requirement-{{ req.id }}"
                                            hx-swap="outerHTML"
                                            hx-on::after-request="if(event.detail.successful) { showSuccessModal('Requirement Updated', 'Requirement status has been updated successfully.', () => window.location.reload()); }"
                                            class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md
                                                   {% if req.is_completed %}text-red-700 bg-red-100 hover:bg-red-200{% else %}text-green-700 bg-green-100 hover:bg-green-200{% endif %}">
                                        {% if req.is_completed %}
                                        <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                        Mark Incomplete
                                        {% else %}
                                        <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                        Mark Complete
                                        {% endif %}
                                    </button>
                                    
                                    <!-- Add Note Button -->
                                    <button type="button" 
                                            onclick="showNoteModal({{ req.id }}, '{{ req.notes|escapejs }}')"
                                            class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                        <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                        </svg>
                                        Note
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Status Card -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Status</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="text-sm font-medium text-gray-500">Current Status</label>
                            <div class="mt-1">
                                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full 
                                    {% if appointment.status == 'pending' %}bg-yellow-100 text-yellow-800
                                    {% elif appointment.status == 'in_progress' %}bg-blue-100 text-blue-800
                                    {% elif appointment.status == 'ready' %}bg-green-100 text-green-800
                                    {% elif appointment.status == 'claimed' %}bg-gray-100 text-gray-800
                                    {% endif %}" id="current-status">
                                    {{ appointment.get_status_display }}
                                </span>
                            </div>
                        </div>
                        
                        <div>
                            <label for="status-select" class="text-sm font-medium text-gray-500">Update Status</label>
                            <select id="status-select" 
                                    hx-post="{% url 'update_appointment_status' appointment.appointment_id %}"
                                    hx-trigger="change"
                                    hx-target="#current-status"
                                    hx-swap="outerHTML"
                                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                                <option value="pending" {% if appointment.status == 'pending' %}selected{% endif %}>Pending</option>
                                <option value="in_progress" {% if appointment.status == 'in_progress' %}selected{% endif %}>In Progress</option>
                                <option value="ready" {% if appointment.status == 'ready' %}selected{% endif %}>Ready</option>
                                <option value="claimed" {% if appointment.status == 'claimed' %}selected{% endif %}>Claimed</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="text-sm font-medium text-gray-500">Progress</label>
                            <div class="mt-1">
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-indigo-600 h-2 rounded-full" style="width: {{ appointment.completion_percentage }}%"></div>
                                </div>
                                <p class="mt-1 text-xs text-gray-500">{{ completed_requirements }}/{{ total_requirements }} requirements completed</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Timeline -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Timeline</h3>
                    <div class="flow-root">
                        <ul role="list" class="-mb-8">
                            <li>
                                <div class="relative pb-8">
                                    <div class="relative flex space-x-3">
                                        <div>
                                            <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                                <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                                                </svg>
                                            </span>
                                        </div>
                                        <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                            <div>
                                                <p class="text-sm text-gray-500">Appointment created</p>
                                            </div>
                                            <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                                {{ appointment.created_at|date:"M d, Y H:i" }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            {% if appointment.updated_at != appointment.created_at %}
                            <li>
                                <div class="relative">
                                    <div class="relative flex space-x-3">
                                        <div>
                                            <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                                <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                                </svg>
                                            </span>
                                        </div>
                                        <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                            <div>
                                                <p class="text-sm text-gray-500">Last updated</p>
                                            </div>
                                            <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                                {{ appointment.updated_at|date:"M d, Y H:i" }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Note Modal -->
<div id="note-modal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
            <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">Add Note</h3>
                <div class="mt-2">
                    <textarea id="note-textarea" rows="4" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="Enter your note here..."></textarea>
                </div>
            </div>
            <div class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
                <button type="button" id="save-note-btn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:col-start-2 sm:text-sm">
                    Save
                </button>
                <button type="button" onclick="hideNoteModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:col-start-1 sm:text-sm">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentRequirementId = null;

function showNoteModal(requirementId, currentNote) {
    currentRequirementId = requirementId;
    document.getElementById('note-textarea').value = currentNote || '';
    document.getElementById('note-modal').classList.remove('hidden');
}

function hideNoteModal() {
    document.getElementById('note-modal').classList.add('hidden');
    currentRequirementId = null;
}

document.getElementById('save-note-btn').addEventListener('click', function() {
    if (!currentRequirementId) return;
    
    const note = document.getElementById('note-textarea').value;
    
    fetch(`{% url 'add_requirement_note' appointment.appointment_id 0 %}`.replace('0', currentRequirementId), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
        },
        body: JSON.stringify({ note: note })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload(); // Reload to show updated note
        } else {
            alert('Error saving note: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving note');
    });
    
    hideNoteModal();
});

// Close modal when clicking outside
document.getElementById('note-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideNoteModal();
    }
});

// Enhanced appointment detail functionality with modal integration
function updateAppointmentStatus(appointmentId, newStatus, currentStatus) {
    const statusLabels = {
        'pending': 'Pending',
        'in_progress': 'In Progress',
        'ready': 'Ready for Pickup',
        'claimed': 'Completed'
    };

    showConfirmationModal(
        'Update Appointment Status',
        `Change status from "${statusLabels[currentStatus]}" to "${statusLabels[newStatus]}"?`,
        async () => {
            showLoadingModal('Updating Status', 'Please wait while we update the appointment status...');

            try {
                const response = await fetch(`/staff/ajax/appointments/${appointmentId}/update-status/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    },
                    body: JSON.stringify({ status: newStatus })
                });

                const data = await response.json();

                if (data.success) {
                    showSuccessModal(
                        'Status Updated',
                        data.message || 'Appointment status has been updated successfully.',
                        () => window.location.reload()
                    );
                } else {
                    showErrorModal('Update Failed', data.message || 'Failed to update appointment status.');
                }
            } catch (error) {
                console.error('Status update error:', error);
                showErrorModal('Network Error', 'Unable to connect to the server. Please try again.');
            }
        },
        'Update Status'
    );
}

// Quick status update buttons
document.addEventListener('click', function(e) {
    if (e.target.matches('[data-action="quick-status-update"]')) {
        e.preventDefault();
        const appointmentId = e.target.dataset.appointmentId;
        const newStatus = e.target.dataset.newStatus;
        const currentStatus = e.target.dataset.currentStatus;
        updateAppointmentStatus(appointmentId, newStatus, currentStatus);
    }

    if (e.target.matches('[data-action="assign-staff"]')) {
        e.preventDefault();
        const appointmentId = e.target.dataset.appointmentId;
        showStaffAssignmentModal(appointmentId);
    }

    if (e.target.matches('[data-action="add-note"]')) {
        e.preventDefault();
        const appointmentId = e.target.dataset.appointmentId;
        showAddNoteModal(appointmentId);
    }
});

// Staff assignment modal
async function showStaffAssignmentModal(appointmentId) {
    try {
        const response = await fetch('/staff/ajax/get-staff-list/');
        const data = await response.json();

        if (data.success) {
            const staffOptions = data.staff.map(staff =>
                `<option value="${staff.id}">${staff.name} - ${staff.department}</option>`
            ).join('');

            const formContent = `
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Assign to Staff Member</label>
                        <select id="staff-select" class="w-full rounded-xl border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500">
                            <option value="">Select staff member...</option>
                            ${staffOptions}
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Assignment Note (Optional)</label>
                        <textarea id="assignment-note" rows="3" class="w-full rounded-xl border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500" placeholder="Add any special instructions..."></textarea>
                    </div>
                </div>
            `;

            showFormModal(
                'Assign Staff Member',
                formContent,
                (modalContent) => processStaffAssignment(appointmentId, modalContent),
                'Assign'
            );
        }
    } catch (error) {
        showErrorModal('Error', 'Unable to load staff list. Please try again.');
    }
}

async function processStaffAssignment(appointmentId, modalContent) {
    const staffId = modalContent.querySelector('#staff-select').value;
    const note = modalContent.querySelector('#assignment-note').value;

    if (!staffId) {
        showErrorModal('Invalid Selection', 'Please select a staff member to assign.');
        return;
    }

    showLoadingModal('Assigning Staff', 'Processing staff assignment...');

    try {
        const response = await fetch(`/staff/ajax/appointments/${appointmentId}/assign/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            },
            body: JSON.stringify({
                staff_id: staffId,
                note: note
            })
        });

        const data = await response.json();

        if (data.success) {
            showSuccessModal(
                'Staff Assigned',
                'Staff member has been assigned to this appointment.',
                () => window.location.reload()
            );
        } else {
            showErrorModal('Assignment Failed', data.message || 'Failed to assign staff member.');
        }
    } catch (error) {
        showErrorModal('Network Error', 'Unable to process assignment. Please try again.');
    }
}

// Add note modal
function showAddNoteModal(appointmentId) {
    const formContent = `
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Internal Note</label>
                <textarea id="note-content" rows="4" class="w-full rounded-xl border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500" placeholder="Add your note here..." required></textarea>
            </div>
            <div>
                <label class="flex items-center">
                    <input type="checkbox" id="note-important" class="rounded border-gray-300 text-emerald-600 shadow-sm focus:border-emerald-300 focus:ring focus:ring-emerald-200 focus:ring-opacity-50">
                    <span class="ml-2 text-sm text-gray-700">Mark as important</span>
                </label>
            </div>
        </div>
    `;

    showFormModal(
        'Add Internal Note',
        formContent,
        (modalContent) => processAddNote(appointmentId, modalContent),
        'Add Note'
    );
}

async function processAddNote(appointmentId, modalContent) {
    const content = modalContent.querySelector('#note-content').value.trim();
    const important = modalContent.querySelector('#note-important').checked;

    if (!content) {
        showErrorModal('Invalid Input', 'Please enter a note before submitting.');
        return;
    }

    showLoadingModal('Adding Note', 'Saving your note...');

    try {
        const response = await fetch(`/staff/ajax/appointments/${appointmentId}/add-note/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            },
            body: JSON.stringify({
                content: content,
                important: important
            })
        });

        const data = await response.json();

        if (data.success) {
            showSuccessModal(
                'Note Added',
                'Your note has been added to the appointment.',
                () => window.location.reload()
            );
        } else {
            showErrorModal('Failed to Add Note', data.message || 'Unable to save the note.');
        }
    } catch (error) {
        showErrorModal('Network Error', 'Unable to save note. Please try again.');
    }
}

// Auto-save functionality for requirement notes
let autoSaveTimeout;
document.addEventListener('input', function(e) {
    if (e.target.matches('[data-auto-save]')) {
        clearTimeout(autoSaveTimeout);
        autoSaveTimeout = setTimeout(() => {
            autoSaveRequirementNote(e.target);
        }, 1000);
    }
});

async function autoSaveRequirementNote(textarea) {
    const appointmentId = textarea.dataset.appointmentId;
    const requirementId = textarea.dataset.requirementId;
    const content = textarea.value;

    try {
        const response = await fetch(`/staff/ajax/appointments/${appointmentId}/auto-save/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            },
            body: JSON.stringify({
                requirement_id: requirementId,
                note: content
            })
        });

        const data = await response.json();

        // Show subtle save indicator
        const indicator = document.querySelector('#auto-save-indicator');
        if (indicator) {
            indicator.textContent = data.success ? 'Saved' : 'Save failed';
            indicator.className = data.success ? 'text-emerald-600' : 'text-red-600';

            setTimeout(() => {
                indicator.textContent = '';
            }, 2000);
        }
    } catch (error) {
        console.error('Auto-save error:', error);
    }
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + S for save
    if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        const activeTextarea = document.querySelector('textarea:focus');
        if (activeTextarea && activeTextarea.dataset.autoSave) {
            autoSaveRequirementNote(activeTextarea);
        }
    }
});
</script>

<!-- Include Modal Components -->
{% include 'components/modals.html' %}

<!-- Auto-save indicator -->
<div id="auto-save-indicator" class="fixed bottom-4 right-4 px-3 py-1 bg-white rounded-lg shadow-lg text-sm font-medium opacity-0 transition-opacity duration-200"></div>

{% endblock %}
