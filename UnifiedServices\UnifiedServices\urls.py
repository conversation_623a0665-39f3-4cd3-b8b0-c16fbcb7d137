"""
URL configuration for UnifiedServices project.
"""
from django.contrib import admin
from django.urls import path, include
from .views import SignUpView
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import RedirectView

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('appointments.urls')),
    path('auth/signup/', SignUpView.as_view(), name='register'),
    path('auth/', include('django.contrib.auth.urls')),
]

# Serve media files during development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATICFILES_DIRS[0])

from django.urls import include
urlpatterns += [path('', include('appointments.urls'))]
