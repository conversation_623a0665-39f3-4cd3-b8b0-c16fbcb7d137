<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign In - JHCSC Unified Student Services</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Custom Tailwind Config for Emerald & Gold Theme -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        emerald: {
                            50: '#ecfdf5',
                            100: '#d1fae5',
                            200: '#a7f3d0',
                            300: '#6ee7b7',
                            400: '#34d399',
                            500: '#10b981',
                            600: '#059669',
                            700: '#047857',
                            800: '#065f46',
                            900: '#064e3b',
                            950: '#022c22'
                        },
                        gold: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f'
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.8s ease-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                        'float': 'float 6s ease-in-out infinite',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' }
                        }
                    }
                }
            }
        }
    </script>

    <style>
        [x-cloak] { display: none !important; }

        .gradient-bg {
            background: linear-gradient(135deg, #059669 0%, #10b981 50%, #f59e0b 100%);
        }

        .glass-effect {
            backdrop-filter: blur(16px);
            background: rgba(255, 255, 255, 0.95);
        }
    </style>
</head>
<body class="h-full gradient-bg">
    <div class="flex min-h-full flex-col justify-center py-12 sm:px-6 lg:px-8 animate-fade-in">
        <!-- Floating Background Elements -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <div class="absolute top-1/4 left-1/4 w-32 h-32 bg-white/10 rounded-full animate-float"></div>
            <div class="absolute top-3/4 right-1/4 w-24 h-24 bg-gold-300/20 rounded-full animate-float" style="animation-delay: -2s;"></div>
            <div class="absolute top-1/2 right-1/3 w-16 h-16 bg-emerald-300/20 rounded-full animate-float" style="animation-delay: -4s;"></div>
        </div>

        <div class="sm:mx-auto sm:w-full sm:max-w-md relative z-10">
            <div class="mx-auto h-20 w-20 flex items-center justify-center rounded-3xl bg-gradient-to-br from-white to-gray-50 shadow-2xl animate-slide-up">
                <svg class="h-12 w-12 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M4.26 10.147a60.436 60.436 0 00-.491 6.347A48.627 48.627 0 0112 20.904a48.627 48.627 0 018.232-4.41 60.46 60.46 0 00-.491-6.347m-15.482 0a50.57 50.57 0 00-2.658-.813A59.905 59.905 0 0112 3.493a59.902 59.902 0 0110.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.697 50.697 0 0112 13.489a50.702 50.702 0 017.74-3.342M6.75 15a.75.75 0 100-1.5.75.75 0 000 1.5zm0 0v-3.675A55.378 55.378 0 0112 8.443a55.381 55.381 0 015.25 2.882V15" />
                </svg>
            </div>
            <h2 class="mt-8 text-center text-4xl font-bold tracking-tight text-white animate-slide-up">
                Welcome Back
            </h2>
            <p class="mt-3 text-center text-lg text-white/90 animate-slide-up">
                JHCSC Unified Student Services Portal
            </p>
        </div>

        <div class="mt-10 sm:mx-auto sm:w-full sm:max-w-md relative z-10">
            <div class="glass-effect py-10 px-6 shadow-2xl rounded-3xl border border-white/20 animate-slide-up">
                {% if messages %}
                    {% for message in messages %}
                    <div class="rounded-2xl p-4 mb-6 {% if message.tags == 'error' %}bg-gradient-to-r from-red-50 to-red-100 border-2 border-red-200{% else %}bg-gradient-to-r from-emerald-50 to-emerald-100 border-2 border-emerald-200{% endif %} animate-slide-up">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                {% if message.tags == 'error' %}
                                <svg class="h-5 w-5 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                {% else %}
                                <svg class="h-5 w-5 text-emerald-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                {% endif %}
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium {% if message.tags == 'error' %}text-red-800{% else %}text-emerald-800{% endif %}">{{ message }}</p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% endif %}

                <form class="space-y-8" method="post" x-data="{ showPassword: false }">
                    {% csrf_token %}
                    <div>
                        <label for="username" class="block text-sm font-semibold text-gray-700 mb-2">Username</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                            </div>
                            <input id="username" name="username" type="text" required
                                   class="block w-full pl-12 pr-4 py-4 text-lg border-2 border-gray-200 rounded-2xl placeholder-gray-400 bg-white/80 backdrop-blur-sm focus:border-emerald-500 focus:ring-emerald-500 focus:bg-white transition-all duration-200 shadow-sm hover:shadow-md"
                                   placeholder="Enter your username">
                        </div>
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-semibold text-gray-700 mb-2">Password</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                            </div>
                            <input id="password" name="password" :type="showPassword ? 'text' : 'password'" required
                                   class="block w-full pl-12 pr-12 py-4 text-lg border-2 border-gray-200 rounded-2xl placeholder-gray-400 bg-white/80 backdrop-blur-sm focus:border-emerald-500 focus:ring-emerald-500 focus:bg-white transition-all duration-200 shadow-sm hover:shadow-md"
                                   placeholder="Enter your password">
                            <div class="absolute inset-y-0 right-0 pr-4 flex items-center">
                                <button type="button" @click="showPassword = !showPassword" class="text-gray-400 hover:text-gray-600 transition-colors duration-200">
                                    <svg x-show="!showPassword" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                    <svg x-show="showPassword" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" x-cloak>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <input id="remember-me" name="remember-me" type="checkbox"
                                   class="h-5 w-5 rounded-lg border-2 border-gray-300 text-emerald-600 focus:ring-emerald-500 focus:ring-offset-0 transition-colors duration-200">
                            <label for="remember-me" class="ml-3 block text-sm font-medium text-gray-700">Remember me</label>
                        </div>

                        <div class="text-sm">
                            <a href="#" class="font-semibold text-emerald-600 hover:text-emerald-700 transition-colors duration-200">Forgot password?</a>
                        </div>
                    </div>

                    <div>
                        <button type="submit"
                                class="group relative w-full flex justify-center py-4 px-6 border border-transparent text-lg font-semibold rounded-2xl text-white bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                            <span class="absolute left-0 inset-y-0 flex items-center pl-4">
                                <svg class="h-6 w-6 text-emerald-300 group-hover:text-emerald-200 transition-colors duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                                </svg>
                            </span>
                            Sign In to Portal
                        </button>
                    </div>
                </form>

                    <div class="mt-8">
                        <div class="relative">
                            <div class="absolute inset-0 flex items-center">
                                <div class="w-full border-t border-gray-200"></div>
                            </div>
                            <div class="relative flex justify-center text-sm">
                                <span class="bg-white px-4 text-gray-500 font-medium">Need assistance?</span>
                            </div>
                        </div>

                        <div class="mt-6 text-center space-y-4">
                            <p class="text-sm text-gray-600">
                                Contact your system administrator for account assistance
                            </p>
                        </div>
                        <div class="mt-8 text-center">
                            <span class="text-gray-600 text-sm">Don't have an account?</span>
                            <a href="{% url 'register' %}" class="ml-2 text-emerald-600 hover:text-emerald-700 font-semibold text-lg transition-colors duration-200">Register</a>
                        </div>
                    </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="mt-8 text-center">
            <p class="text-white/80 text-sm">
                © 2024 Jose Hernandez College of Science and Computing. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
