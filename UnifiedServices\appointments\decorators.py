"""
Decorators for appointment management views
"""

from functools import wraps
from django.http import JsonResponse
from django.shortcuts import redirect
from django.contrib.auth.decorators import login_required
from django.core.exceptions import PermissionDenied


def staff_required(view_func):
    """
    Decorator that requires the user to be staff (office_staff or admin)
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_authenticated:
            if request.headers.get('Content-Type') == 'application/json':
                return JsonResponse({'success': False, 'message': 'Authentication required.'}, status=401)
            return redirect('login')
        
        try:
            user_profile = request.user.userprofile
            if user_profile.role not in ['office_staff', 'admin']:
                if request.headers.get('Content-Type') == 'application/json':
                    return JsonResponse({'success': False, 'message': 'Staff access required.'}, status=403)
                raise PermissionDenied("Staff access required.")
        except AttributeError:
            if request.headers.get('Content-Type') == 'application/json':
                return JsonResponse({'success': False, 'message': 'User profile not found.'}, status=403)
            raise PermissionDenied("User profile not found.")
        
        return view_func(request, *args, **kwargs)
    return _wrapped_view


def admin_required(view_func):
    """
    Decorator that requires the user to be an admin
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_authenticated:
            if request.headers.get('Content-Type') == 'application/json':
                return JsonResponse({'success': False, 'message': 'Authentication required.'}, status=401)
            return redirect('login')
        
        try:
            user_profile = request.user.userprofile
            if user_profile.role != 'admin':
                if request.headers.get('Content-Type') == 'application/json':
                    return JsonResponse({'success': False, 'message': 'Admin access required.'}, status=403)
                raise PermissionDenied("Admin access required.")
        except AttributeError:
            if request.headers.get('Content-Type') == 'application/json':
                return JsonResponse({'success': False, 'message': 'User profile not found.'}, status=403)
            raise PermissionDenied("User profile not found.")
        
        return view_func(request, *args, **kwargs)
    return _wrapped_view


def student_required(view_func):
    """
    Decorator that requires the user to be a student
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_authenticated:
            if request.headers.get('Content-Type') == 'application/json':
                return JsonResponse({'success': False, 'message': 'Authentication required.'}, status=401)
            return redirect('login')
        
        try:
            user_profile = request.user.userprofile
            if user_profile.role != 'student':
                if request.headers.get('Content-Type') == 'application/json':
                    return JsonResponse({'success': False, 'message': 'Student access required.'}, status=403)
                raise PermissionDenied("Student access required.")
        except AttributeError:
            if request.headers.get('Content-Type') == 'application/json':
                return JsonResponse({'success': False, 'message': 'User profile not found.'}, status=403)
            raise PermissionDenied("User profile not found.")
        
        return view_func(request, *args, **kwargs)
    return _wrapped_view
